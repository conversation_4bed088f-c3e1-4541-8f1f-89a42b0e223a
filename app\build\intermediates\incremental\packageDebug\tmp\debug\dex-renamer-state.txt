#Fri Aug 08 00:23:22 CST 2025
base.0=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.10=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.11=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.2=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\11\\classes.dex
base.3=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\12\\classes.dex
base.4=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
base.5=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.6=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\4\\classes.dex
base.7=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
base.8=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.9=C\:\\WorkSpace\\hongshu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.10=8/classes.dex
path.11=classes2.dex
path.2=11/classes.dex
path.3=12/classes.dex
path.4=2/classes.dex
path.5=3/classes.dex
path.6=4/classes.dex
path.7=5/classes.dex
path.8=6/classes.dex
path.9=7/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
