package com.xue.hongshu.ui.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog

@Composable
fun AddEmployeeDialog(
    onDismiss: () -> Unit,
    onConfirm: (name: String, xhsUserId: String, xhsUserName: String) -> Unit
) {
    var name by remember { mutableStateOf("") }
    var xhsUserId by remember { mutableStateOf("") }
    var xhsUserName by remember { mutableStateOf("") }
    var nameError by remember { mutableStateOf(false) }
    var userIdError by remember { mutableStateOf(false) }
    var userNameError by remember { mutableStateOf(false) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "添加员工",
                    style = MaterialTheme.typography.headlineSmall
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 员工姓名
                OutlinedTextField(
                    value = name,
                    onValueChange = { 
                        name = it
                        nameError = false
                    },
                    label = { Text("员工姓名") },
                    isError = nameError,
                    supportingText = if (nameError) {
                        { Text("请输入员工姓名") }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 小红书用户ID
                OutlinedTextField(
                    value = xhsUserId,
                    onValueChange = { 
                        xhsUserId = it
                        userIdError = false
                    },
                    label = { Text("小红书用户ID") },
                    placeholder = { Text("例如: 5f1234567890abcdef123456") },
                    isError = userIdError,
                    supportingText = if (userIdError) {
                        { Text("请输入有效的用户ID") }
                    } else {
                        { Text("从小红书用户主页URL中获取") }
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 小红书用户名
                OutlinedTextField(
                    value = xhsUserName,
                    onValueChange = { 
                        xhsUserName = it
                        userNameError = false
                    },
                    label = { Text("小红书用户名") },
                    placeholder = { Text("例如: 张三的小红书") },
                    isError = userNameError,
                    supportingText = if (userNameError) {
                        { Text("请输入用户名") }
                    } else {
                        { Text("用于显示和识别") }
                    },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            // 验证输入
                            nameError = name.isBlank()
                            userIdError = xhsUserId.isBlank()
                            userNameError = xhsUserName.isBlank()
                            
                            if (!nameError && !userIdError && !userNameError) {
                                onConfirm(name.trim(), xhsUserId.trim(), xhsUserName.trim())
                            }
                        }
                    ) {
                        Text("添加")
                    }
                }
            }
        }
    }
}
