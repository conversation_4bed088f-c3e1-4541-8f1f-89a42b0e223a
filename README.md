# 小红书员工监控系统

一个基于Android的企业级员工小红书发布活动监控应用，采用现代化技术栈和智能反检测策略。

## 🚀 功能特性

### 核心功能
- **员工管理**：添加、删除、编辑员工信息和小红书账号绑定
- **智能监控**：自动检查员工最新发布状态和时间
- **批量处理**：一键检查所有员工的发布情况
- **实时统计**：直观显示员工活跃度和超期情况

### 技术特性
- **企业级反爬虫**：多层次反检测策略，高成功率数据获取
- **智能错误处理**：自适应重试机制和错误类型分析
- **实时监控**：系统状态、网络质量、错误统计可视化
- **完整日志**：详细的操作记录和错误追踪

## 🏗️ 技术架构

### 架构模式
- **MVVM架构**：清晰的业务逻辑分离
- **Repository模式**：统一的数据访问层
- **单一数据源**：Room数据库本地存储

### 技术栈
- **UI框架**：Jetpack Compose
- **数据库**：Room + SQLite
- **网络**：OkHttp + Retrofit
- **异步处理**：Kotlin Coroutines + Flow
- **WebView**：自定义WebViewClient + JavaScript注入

## 📱 界面预览

### 主界面
- 员工列表显示
- 统计信息卡片
- 系统监控面板
- 批量操作按钮

### 功能界面
- 员工添加对话框
- WebView数据抓取界面
- 日志查看器
- 系统状态监控

## 🛠️ 安装部署

### 环境要求
- Android Studio Hedgehog | 2023.1.1 或更高版本
- Android SDK API 24 (Android 7.0) 或更高版本
- Kotlin 1.9.0 或更高版本

### 编译步骤
1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd hongshu
   ```

2. **打开项目**
   - 使用Android Studio打开项目
   - 等待Gradle同步完成

3. **配置签名**（可选）
   - 在`app/build.gradle.kts`中配置签名信息
   - 或使用Debug签名进行测试

4. **编译安装**
   ```bash
   ./gradlew assembleDebug
   # 或在Android Studio中点击Run按钮
   ```

### 权限说明
应用需要以下权限：
- `INTERNET`：网络访问权限
- `ACCESS_NETWORK_STATE`：网络状态检测

## 📖 使用指南

### 基本使用
1. **添加员工**
   - 点击右下角"+"按钮
   - 输入员工姓名、小红书用户ID和用户名
   - 确认添加

2. **检查单个员工**
   - 在员工卡片上点击"检查"按钮
   - 系统自动打开WebView进行数据抓取
   - 等待结果更新

3. **批量检查**
   - 点击顶部刷新按钮
   - 系统依次检查所有员工
   - 查看进度条和结果

4. **查看系统状态**
   - 点击顶部信息按钮展开系统监控
   - 查看网络状态、错误统计等信息
   - 可查看详细日志或重置配置

### 高级功能
- **日志查看**：点击"查看日志"按钮查看详细运行日志
- **配置调整**：系统会根据成功率自动调整检测策略
- **错误分析**：查看错误统计了解系统运行状况

## ⚙️ 配置说明

### 检测级别
- **低级别**：基础反检测，适合网络环境良好的情况
- **中级别**：增强反检测，适合一般网络环境
- **高级别**：最强反检测，适合严格的网络环境

### 策略模式
- **正常模式**：标准的检测策略和延迟
- **隐蔽模式**：更长的延迟和更强的反检测
- **激进模式**：更短的延迟，适合快速检测

## 🔧 故障排除

### 常见问题
1. **网络连接失败**
   - 检查网络连接
   - 确认小红书网站可正常访问
   - 尝试切换网络环境

2. **数据解析失败**
   - 可能是小红书页面结构发生变化
   - 查看日志了解具体错误信息
   - 等待应用更新适配

3. **检测被阻止**
   - 系统会自动提升检测级别
   - 可手动切换到隐蔽模式
   - 适当增加检测间隔

### 日志分析
- **ERROR级别**：需要关注的错误信息
- **WARN级别**：警告信息，可能影响功能
- **INFO级别**：正常的操作记录
- **DEBUG级别**：详细的调试信息

## 🚨 重要提醒

### 合规使用
- 本应用仅供企业内部管理使用
- 使用前请确保已获得员工同意
- 请遵守相关法律法规和平台服务条款

### 技术风险
- 小红书可能更新反爬虫策略导致功能失效
- 频繁使用可能触发平台限制
- 建议合理控制检测频率

### 数据安全
- 所有数据仅存储在本地设备
- 不会上传任何用户信息到外部服务器
- 建议定期备份重要数据

## 📞 技术支持

如遇到技术问题，请：
1. 查看应用内的日志信息
2. 检查网络连接和权限设置
3. 尝试重启应用或清除缓存
4. 联系技术支持团队

## 📄 开源协议

本项目采用 MIT 协议开源，详见 LICENSE 文件。

---

**免责声明**：本应用仅供学习和企业内部管理使用，使用者需自行承担使用风险，开发者不对任何损失负责。
