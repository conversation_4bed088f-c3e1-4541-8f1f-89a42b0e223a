package com.xue.hongshu.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import java.io.IOException
import java.net.URL
import java.security.cert.X509Certificate
import java.text.SimpleDateFormat
import java.util.*
import javax.net.ssl.*

/**
 * SSL连接诊断工具
 * 用于检测和诊断SSL连接问题
 */
object SslDiagnosticTool {
    
    private const val TAG = "SslDiagnosticTool"
    
    /**
     * SSL诊断结果
     */
    data class SslDiagnosticResult(
        val url: String,
        val isSuccess: Boolean,
        val errorMessage: String? = null,
        val certificateInfo: CertificateInfo? = null,
        val connectionInfo: ConnectionInfo? = null,
        val recommendations: List<String> = emptyList()
    )
    
    /**
     * 证书信息
     */
    data class CertificateInfo(
        val subject: String,
        val issuer: String,
        val validFrom: Date,
        val validTo: Date,
        val isExpired: Boolean,
        val daysUntilExpiry: Long,
        val signatureAlgorithm: String,
        val serialNumber: String
    )
    
    /**
     * 连接信息
     */
    data class ConnectionInfo(
        val protocol: String,
        val cipherSuite: String,
        val peerHost: String,
        val peerPort: Int,
        val localCertificates: List<String>,
        val peerCertificates: List<String>
    )
    
    /**
     * 诊断SSL连接
     */
    suspend fun diagnoseSslConnection(
        urlString: String,
        timeout: Int = 10000
    ): SslDiagnosticResult = withContext(Dispatchers.IO) {
        
        Log.d(TAG, "开始诊断SSL连接: $urlString")
        
        try {
            val url = URL(urlString)
            val connection = url.openConnection() as HttpsURLConnection
            
            // 设置超时
            connection.connectTimeout = timeout
            connection.readTimeout = timeout
            
            // 设置自定义SSL上下文以获取详细信息
            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(null, arrayOf(DiagnosticTrustManager()), null)
            connection.sslSocketFactory = sslContext.socketFactory
            
            // 设置主机名验证器
            connection.hostnameVerifier = DiagnosticHostnameVerifier()
            
            // 尝试连接
            connection.connect()

            // 获取SSL证书信息
            val certificates = connection.serverCertificates

            // 创建基本的连接信息（不依赖SSL会话）
            val sslSession: SSLSession? = null // SSL会话信息在某些Android版本中不可直接访问

            if (certificates != null && certificates.isNotEmpty()) {
                val serverCert = certificates[0] as X509Certificate
                val certInfo = extractCertificateInfo(serverCert)
                val connInfo = extractConnectionInfo(connection, sslSession)
                
                val recommendations = generateRecommendations(certInfo, connInfo)
                
                Log.d(TAG, "SSL连接诊断成功: $urlString")
                
                SslDiagnosticResult(
                    url = urlString,
                    isSuccess = true,
                    certificateInfo = certInfo,
                    connectionInfo = connInfo,
                    recommendations = recommendations
                )
            } else {
                Log.w(TAG, "无法获取证书信息: $urlString")
                SslDiagnosticResult(
                    url = urlString,
                    isSuccess = false,
                    errorMessage = "无法获取服务器证书信息",
                    recommendations = listOf("检查服务器SSL配置", "确认URL是否正确")
                )
            }
            
        } catch (e: SSLException) {
            Log.e(TAG, "SSL异常: $urlString", e)
            handleSslException(urlString, e)
        } catch (e: IOException) {
            Log.e(TAG, "网络异常: $urlString", e)
            SslDiagnosticResult(
                url = urlString,
                isSuccess = false,
                errorMessage = "网络连接失败: ${e.message}",
                recommendations = listOf(
                    "检查网络连接",
                    "确认服务器是否可访问",
                    "检查防火墙设置"
                )
            )
        } catch (e: Exception) {
            Log.e(TAG, "未知异常: $urlString", e)
            SslDiagnosticResult(
                url = urlString,
                isSuccess = false,
                errorMessage = "连接失败: ${e.message}",
                recommendations = listOf("检查URL格式", "联系技术支持")
            )
        }
    }
    
    /**
     * 批量诊断多个URL
     */
    suspend fun diagnoseBatch(urls: List<String>): List<SslDiagnosticResult> {
        return urls.map { url ->
            try {
                diagnoseSslConnection(url)
            } catch (e: Exception) {
                Log.e(TAG, "批量诊断失败: $url", e)
                SslDiagnosticResult(
                    url = url,
                    isSuccess = false,
                    errorMessage = "诊断失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 提取证书信息
     */
    private fun extractCertificateInfo(certificate: X509Certificate): CertificateInfo {
        val now = Date()
        val validTo = certificate.notAfter
        val daysUntilExpiry = (validTo.time - now.time) / (1000 * 60 * 60 * 24)
        
        return CertificateInfo(
            subject = certificate.subjectDN.name,
            issuer = certificate.issuerDN.name,
            validFrom = certificate.notBefore,
            validTo = validTo,
            isExpired = now.after(validTo),
            daysUntilExpiry = daysUntilExpiry,
            signatureAlgorithm = certificate.sigAlgName,
            serialNumber = certificate.serialNumber.toString(16)
        )
    }
    
    /**
     * 提取连接信息
     */
    private fun extractConnectionInfo(connection: HttpsURLConnection, sslSession: SSLSession?): ConnectionInfo? {
        return try {
            // 从连接中提取基本信息
            val url = connection.url
            val host = url.host
            val port = if (url.port != -1) url.port else url.defaultPort

            // 如果有SSL会话信息，使用它；否则使用基本信息
            if (sslSession != null) {
                ConnectionInfo(
                    protocol = sslSession.protocol,
                    cipherSuite = sslSession.cipherSuite,
                    peerHost = sslSession.peerHost,
                    peerPort = sslSession.peerPort,
                    localCertificates = sslSession.localCertificates?.map { it.toString() } ?: emptyList(),
                    peerCertificates = sslSession.peerCertificates?.map { it.toString() } ?: emptyList()
                )
            } else {
                // 创建基本连接信息
                ConnectionInfo(
                    protocol = "TLS", // 默认协议
                    cipherSuite = "未知", // 无法获取具体的加密套件
                    peerHost = host,
                    peerPort = port,
                    localCertificates = emptyList(),
                    peerCertificates = connection.serverCertificates?.map { it.toString() } ?: emptyList()
                )
            }
        } catch (e: Exception) {
            Log.w(TAG, "提取连接信息失败", e)
            null
        }
    }
    
    /**
     * 处理SSL异常
     */
    private fun handleSslException(url: String, e: SSLException): SslDiagnosticResult {
        val errorMessage = e.message ?: "未知SSL错误"
        val recommendations = mutableListOf<String>()
        
        when {
            errorMessage.contains("certificate", ignoreCase = true) -> {
                recommendations.addAll(listOf(
                    "检查服务器证书是否有效",
                    "确认证书是否过期",
                    "检查证书颁发机构是否受信任"
                ))
            }
            errorMessage.contains("handshake", ignoreCase = true) -> {
                recommendations.addAll(listOf(
                    "检查TLS版本兼容性",
                    "确认加密套件支持",
                    "检查网络连接稳定性"
                ))
            }
            errorMessage.contains("hostname", ignoreCase = true) -> {
                recommendations.addAll(listOf(
                    "检查证书域名是否匹配",
                    "确认访问的域名是否正确",
                    "检查DNS解析"
                ))
            }
            else -> {
                recommendations.addAll(listOf(
                    "检查SSL/TLS配置",
                    "确认服务器支持的协议版本",
                    "联系服务器管理员"
                ))
            }
        }
        
        return SslDiagnosticResult(
            url = url,
            isSuccess = false,
            errorMessage = "SSL连接失败: $errorMessage",
            recommendations = recommendations
        )
    }
    
    /**
     * 生成建议
     */
    private fun generateRecommendations(
        certInfo: CertificateInfo,
        connInfo: ConnectionInfo?
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 证书过期检查
        if (certInfo.isExpired) {
            recommendations.add("⚠️ 证书已过期，需要更新证书")
        } else if (certInfo.daysUntilExpiry < 30) {
            recommendations.add("⚠️ 证书将在${certInfo.daysUntilExpiry}天后过期，建议提前更新")
        }
        
        // 协议版本检查
        connInfo?.let { info ->
            when (info.protocol) {
                "TLSv1", "TLSv1.1" -> {
                    recommendations.add("⚠️ 使用较旧的TLS版本(${info.protocol})，建议升级到TLS 1.2或1.3")
                }
                "TLSv1.2" -> {
                    recommendations.add("✅ 使用TLS 1.2，安全性良好")
                }
                "TLSv1.3" -> {
                    recommendations.add("✅ 使用最新的TLS 1.3，安全性最佳")
                }
            }
        }
        
        // 签名算法检查
        if (certInfo.signatureAlgorithm.contains("SHA1", ignoreCase = true)) {
            recommendations.add("⚠️ 证书使用SHA1签名算法，安全性较低，建议使用SHA256或更高")
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("✅ SSL配置良好，未发现明显问题")
        }
        
        return recommendations
    }
    
    /**
     * 诊断用信任管理器
     */
    private class DiagnosticTrustManager : X509TrustManager {
        override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
        
        override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
            // 在诊断模式下，记录证书信息但不抛出异常
            Log.d(TAG, "服务器证书链长度: ${chain.size}")
            chain.forEachIndexed { index, cert ->
                Log.d(TAG, "证书[$index]: ${cert.subjectDN.name}")
            }
        }
        
        override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
    }
    
    /**
     * 诊断用主机名验证器
     */
    private class DiagnosticHostnameVerifier : HostnameVerifier {
        override fun verify(hostname: String, session: SSLSession): Boolean {
            Log.d(TAG, "主机名验证: $hostname")
            return true // 诊断模式下总是返回true
        }
    }
}
