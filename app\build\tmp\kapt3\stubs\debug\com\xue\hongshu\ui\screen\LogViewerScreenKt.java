package com.xue.hongshu.ui.screen;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u001a\u001a\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a\u0016\u0010\f\u001a\u00020\u00012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u00a8\u0006\u000f"}, d2 = {"LogFilterBar", "", "selectedFilter", "", "onFilterChanged", "Lkotlin/Function1;", "logStats", "Lcom/xue/hongshu/utils/LogStats;", "LogItem", "logLine", "modifier", "Landroidx/compose/ui/Modifier;", "LogViewerScreen", "onBackPressed", "Lkotlin/Function0;", "app_debug"})
public final class LogViewerScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void LogViewerScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackPressed) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LogFilterBar(java.lang.String selectedFilter, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onFilterChanged, com.xue.hongshu.utils.LogStats logStats) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LogItem(java.lang.String logLine, androidx.compose.ui.Modifier modifier) {
    }
}