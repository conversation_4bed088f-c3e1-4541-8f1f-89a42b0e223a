package com.xue.hongshu.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u0000 \u00112\u00020\u0001:\u0001\u0011B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0002J\u0010\u0010\f\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\u0004H\u0002J\u0012\u0010\u000e\u001a\u00020\t2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0014R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/xue/hongshu/activity/WebViewActivity;", "Landroidx/activity/ComponentActivity;", "()V", "employeeId", "", "repository", "Lcom/xue/hongshu/repository/EmployeeRepository;", "xhsUserId", "handleDataExtracted", "", "data", "Lcom/xue/hongshu/webview/XhsUserData;", "handleError", "error", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "Companion", "app_release"})
public final class WebViewActivity extends androidx.activity.ComponentActivity {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EXTRA_EMPLOYEE_ID = "employee_id";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EXTRA_XHS_USER_ID = "xhs_user_id";
    private com.xue.hongshu.repository.EmployeeRepository repository;
    private java.lang.String employeeId;
    private java.lang.String xhsUserId;
    @org.jetbrains.annotations.NotNull()
    public static final com.xue.hongshu.activity.WebViewActivity.Companion Companion = null;
    
    public WebViewActivity() {
        super(0);
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void handleDataExtracted(com.xue.hongshu.webview.XhsUserData data) {
    }
    
    private final void handleError(java.lang.String error) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/xue/hongshu/activity/WebViewActivity$Companion;", "", "()V", "EXTRA_EMPLOYEE_ID", "", "EXTRA_XHS_USER_ID", "createIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "employeeId", "xhsUserId", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.content.Intent createIntent(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        java.lang.String employeeId, @org.jetbrains.annotations.NotNull()
        java.lang.String xhsUserId) {
            return null;
        }
    }
}