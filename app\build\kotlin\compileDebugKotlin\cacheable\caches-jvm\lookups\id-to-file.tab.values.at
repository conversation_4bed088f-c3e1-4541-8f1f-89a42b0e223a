/ Header Record For PersistentHashMapValueStorage2 1app/src/main/java/com/xue/hongshu/MainActivity.kt> =app/src/main/java/com/xue/hongshu/activity/WebViewActivity.kt: 9app/src/main/java/com/xue/hongshu/data/dao/EmployeeDao.kt? >app/src/main/java/com/xue/hongshu/data/database/AppDatabase.kt> =app/src/main/java/com/xue/hongshu/data/database/Converters.kt: 9app/src/main/java/com/xue/hongshu/data/entity/Employee.kt> =app/src/main/java/com/xue/hongshu/network/SecureHttpClient.ktC Bapp/src/main/java/com/xue/hongshu/repository/EmployeeRepository.ktD Capp/src/main/java/com/xue/hongshu/ui/component/AddEmployeeDialog.kt? >app/src/main/java/com/xue/hongshu/ui/component/EmployeeCard.ktA @app/src/main/java/com/xue/hongshu/ui/component/StatisticsCard.ktD Capp/src/main/java/com/xue/hongshu/ui/component/SystemMonitorCard.kt? >app/src/main/java/com/xue/hongshu/ui/screen/LogViewerScreen.kt: 9app/src/main/java/com/xue/hongshu/ui/screen/MainScreen.kt4 3app/src/main/java/com/xue/hongshu/ui/theme/Color.kt4 3app/src/main/java/com/xue/hongshu/ui/theme/Theme.kt3 2app/src/main/java/com/xue/hongshu/ui/theme/Type.kt= <app/src/main/java/com/xue/hongshu/utils/CertificateHelper.kt9 8app/src/main/java/com/xue/hongshu/utils/ConfigManager.kt8 7app/src/main/java/com/xue/hongshu/utils/ErrorHandler.kt2 1app/src/main/java/com/xue/hongshu/utils/Logger.kt: 9app/src/main/java/com/xue/hongshu/utils/NetworkMonitor.kt< ;app/src/main/java/com/xue/hongshu/utils/SslConfigManager.kt= <app/src/main/java/com/xue/hongshu/utils/SslDiagnosticTool.kt9 8app/src/main/java/com/xue/hongshu/utils/WebViewTester.kt= <app/src/main/java/com/xue/hongshu/viewmodel/MainViewModel.ktD Capp/src/main/java/com/xue/hongshu/viewmodel/MainViewModelFactory.ktA @app/src/main/java/com/xue/hongshu/webview/AntiDetectionHelper.kt> =app/src/main/java/com/xue/hongshu/webview/XhsWebViewClient.kt> =app/src/main/java/com/xue/hongshu/webview/XhsWebViewClient.kt