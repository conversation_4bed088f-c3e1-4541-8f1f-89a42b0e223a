package com.xue.hongshu

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.xue.hongshu.data.database.AppDatabase
import com.xue.hongshu.ui.screen.LogViewerScreen
import com.xue.hongshu.utils.Logger
import kotlinx.coroutines.launch
import com.xue.hongshu.repository.EmployeeRepository
import com.xue.hongshu.ui.screen.MainScreen
import com.xue.hongshu.ui.theme.HongshuTheme
import com.xue.hongshu.viewmodel.MainViewModel
import com.xue.hongshu.viewmodel.MainViewModelFactory

class MainActivity : ComponentActivity() {

    private lateinit var database: AppDatabase
    private lateinit var repository: EmployeeRepository

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化Logger
        Logger.init(this)
        Logger.i("MainActivity", "应用启动")

        // 初始化数据库和Repository
        database = AppDatabase.getDatabase(this)
        repository = EmployeeRepository(database.employeeDao())

        enableEdgeToEdge()
        setContent {
            HongshuTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    var currentScreen by remember { mutableStateOf("main") }

                    when (currentScreen) {
                        "main" -> {
                            val viewModel: MainViewModel = viewModel(
                                factory = MainViewModelFactory(repository)
                            )
                            MainScreen(
                                viewModel = viewModel,
                                onNavigateToWebView = { employeeId ->
                                    Logger.logUserAction("启动WebView检查", "员工ID: $employeeId")
                                    startWebViewActivity(employeeId)
                                },
                                onNavigateToLogs = {
                                    Logger.logUserAction("查看日志")
                                    currentScreen = "logs"
                                }
                            )
                        }
                        "logs" -> {
                            LogViewerScreen(
                                onBackPressed = {
                                    Logger.logUserAction("返回主界面")
                                    currentScreen = "main"
                                }
                            )
                        }
                    }
                }
            }
        }
    }

    private fun startWebViewActivity(employeeId: String) {
        // 获取员工信息并启动WebView Activity
        lifecycleScope.launch {
            try {
                val employee = repository.getEmployeeById(employeeId)
                if (employee != null) {
                    Logger.logUserAction("启动WebView", "员工: ${employee.name}, 小红书ID: ${employee.xhsUserId}")
                    val intent = com.xue.hongshu.activity.WebViewActivity.createIntent(
                        this@MainActivity,
                        employeeId,
                        employee.xhsUserId
                    )
                    startActivity(intent)
                } else {
                    Logger.w("MainActivity", "员工信息不存在: $employeeId")
                    android.widget.Toast.makeText(
                        this@MainActivity,
                        "员工信息不存在",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                }
            } catch (e: Exception) {
                Logger.e("MainActivity", "启动检查失败", e)
                android.widget.Toast.makeText(
                    this@MainActivity,
                    "启动检查失败: ${e.message}",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        }
    }
}