1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.xue.hongshu"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.xue.hongshu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.xue.hongshu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:8:5-36:19
21        android:allowBackup="true"
21-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:13:9-41
29        android:networkSecurityConfig="@xml/network_security_config"
29-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:17:9-69
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:14:9-54
31        android:supportsRtl="true"
31-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:15:9-35
32        android:testOnly="true"
33        android:theme="@style/Theme.Hongshu"
33-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:16:9-45
34        android:usesCleartextTraffic="true" >
34-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:18:9-44
35        <activity
35-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:19:9-29:20
36            android:name="com.xue.hongshu.MainActivity"
36-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:20:13-41
37            android:exported="true"
37-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:21:13-36
38            android:label="@string/app_name"
38-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:22:13-45
39            android:theme="@style/Theme.Hongshu" >
39-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:23:13-49
40            <intent-filter>
40-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:24:13-28:29
41                <action android:name="android.intent.action.MAIN" />
41-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:25:17-69
41-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:25:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:27:17-77
43-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:27:27-74
44            </intent-filter>
45        </activity>
46        <activity
46-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:31:9-35:52
47            android:name="com.xue.hongshu.activity.WebViewActivity"
47-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:32:13-53
48            android:exported="false"
48-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:33:13-37
49            android:label="数据检查"
49-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:34:13-33
50            android:theme="@style/Theme.Hongshu" />
50-->C:\WorkSpace\hongshu\app\src\main\AndroidManifest.xml:35:13-49
51        <activity
51-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
52            android:name="androidx.compose.ui.tooling.PreviewActivity"
52-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
53            android:exported="true" />
53-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
54
55        <provider
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
56            android:name="androidx.startup.InitializationProvider"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
57            android:authorities="com.xue.hongshu.androidx-startup"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
58            android:exported="false" >
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
59            <meta-data
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.emoji2.text.EmojiCompatInitializer"
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
61                android:value="androidx.startup" />
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
63-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
64                android:value="androidx.startup" />
64-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
67                android:value="androidx.startup" />
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
68        </provider>
69
70        <activity
70-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
71            android:name="androidx.activity.ComponentActivity"
71-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
72            android:exported="true" />
72-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
73
74        <service
74-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f23e5a60ee81741cd5e698bb7bb10d9c\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
75            android:name="androidx.room.MultiInstanceInvalidationService"
75-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f23e5a60ee81741cd5e698bb7bb10d9c\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
76            android:directBootAware="true"
76-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f23e5a60ee81741cd5e698bb7bb10d9c\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
77            android:exported="false" />
77-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f23e5a60ee81741cd5e698bb7bb10d9c\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
78
79        <receiver
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
80            android:name="androidx.profileinstaller.ProfileInstallReceiver"
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
81            android:directBootAware="false"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
82            android:enabled="true"
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
83            android:exported="true"
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
84            android:permission="android.permission.DUMP" >
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
86                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
89                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
92                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
95                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
96            </intent-filter>
97        </receiver>
98    </application>
99
100</manifest>
