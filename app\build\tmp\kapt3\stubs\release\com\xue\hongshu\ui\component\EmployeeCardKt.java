package com.xue.hongshu.ui.component;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\u001a>\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001a\u0018\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u0005H\u0003\u001a\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0002\u00a8\u0006\u0013"}, d2 = {"EmployeeCard", "", "employeeWithStats", "Lcom/xue/hongshu/data/entity/EmployeeWithStats;", "isChecking", "", "onCheckClick", "Lkotlin/Function0;", "onDeleteClick", "modifier", "Landroidx/compose/ui/Modifier;", "StatusChip", "status", "Lcom/xue/hongshu/data/entity/EmployeeStatus;", "isOverdue", "formatTime", "", "timestamp", "", "app_release"})
public final class EmployeeCardKt {
    
    @androidx.compose.runtime.Composable()
    public static final void EmployeeCard(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeWithStats employeeWithStats, boolean isChecking, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCheckClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDeleteClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatusChip(com.xue.hongshu.data.entity.EmployeeStatus status, boolean isOverdue) {
    }
    
    private static final java.lang.String formatTime(long timestamp) {
        return null;
    }
}