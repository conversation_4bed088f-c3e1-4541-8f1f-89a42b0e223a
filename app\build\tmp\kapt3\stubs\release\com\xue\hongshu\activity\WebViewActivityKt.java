package com.xue.hongshu.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001aN\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u00062\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00062\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u00a8\u0006\u000b"}, d2 = {"WebViewScreen", "", "employeeId", "", "xhsUserId", "onDataExtracted", "Lkotlin/Function1;", "Lcom/xue/hongshu/webview/XhsUserData;", "onError", "onBackPressed", "Lkotlin/Function0;", "app_release"})
public final class WebViewActivityKt {
    
    @android.annotation.SuppressLint(value = {"SetJavaScriptEnabled"})
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void WebViewScreen(@org.jetbrains.annotations.NotNull()
    java.lang.String employeeId, @org.jetbrains.annotations.NotNull()
    java.lang.String xhsUserId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.xue.hongshu.webview.XhsUserData, kotlin.Unit> onDataExtracted, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackPressed) {
    }
}