package com.xue.hongshu.utils

import android.content.Context
import android.content.SharedPreferences
import kotlin.random.Random

class ConfigManager(context: Context) {
    
    companion object {
        private const val PREFS_NAME = "hongshu_config"
        private const val KEY_DETECTION_LEVEL = "detection_level"
        private const val KEY_RETRY_COUNT = "retry_count"
        private const val KEY_BASE_DELAY = "base_delay"
        private const val KEY_USER_AGENT_INDEX = "user_agent_index"
        private const val KEY_LAST_SUCCESS_TIME = "last_success_time"
        private const val KEY_CONSECUTIVE_FAILURES = "consecutive_failures"
        private const val KEY_STRATEGY_MODE = "strategy_mode"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    enum class DetectionLevel {
        LOW,     // 低检测风险 - 使用基础策略
        MEDIUM,  // 中等检测风险 - 使用增强策略
        HIGH     // 高检测风险 - 使用最强策略
    }
    
    enum class StrategyMode {
        NORMAL,    // 正常模式
        STEALTH,   // 隐蔽模式
        AGGRESSIVE // 激进模式
    }
    
    // 获取当前检测级别
    fun getDetectionLevel(): DetectionLevel {
        val level = prefs.getString(KEY_DETECTION_LEVEL, DetectionLevel.LOW.name)
        return DetectionLevel.valueOf(level ?: DetectionLevel.LOW.name)
    }
    
    // 设置检测级别
    fun setDetectionLevel(level: DetectionLevel) {
        prefs.edit().putString(KEY_DETECTION_LEVEL, level.name).apply()
    }
    
    // 获取重试次数
    fun getRetryCount(): Int {
        return prefs.getInt(KEY_RETRY_COUNT, 3)
    }
    
    // 设置重试次数
    fun setRetryCount(count: Int) {
        prefs.edit().putInt(KEY_RETRY_COUNT, count).apply()
    }
    
    // 获取基础延迟
    fun getBaseDelay(): Long {
        return prefs.getLong(KEY_BASE_DELAY, 2000L)
    }
    
    // 设置基础延迟
    fun setBaseDelay(delay: Long) {
        prefs.edit().putLong(KEY_BASE_DELAY, delay).apply()
    }
    
    // 获取User-Agent索引
    fun getUserAgentIndex(): Int {
        return prefs.getInt(KEY_USER_AGENT_INDEX, 0)
    }
    
    // 轮换User-Agent
    fun rotateUserAgent(maxIndex: Int) {
        val currentIndex = getUserAgentIndex()
        val nextIndex = (currentIndex + 1) % maxIndex
        prefs.edit().putInt(KEY_USER_AGENT_INDEX, nextIndex).apply()
    }
    
    // 记录成功时间
    fun recordSuccess() {
        prefs.edit()
            .putLong(KEY_LAST_SUCCESS_TIME, System.currentTimeMillis())
            .putInt(KEY_CONSECUTIVE_FAILURES, 0)
            .apply()
        
        // 成功后可以降低检测级别
        adjustDetectionLevel(false)
    }
    
    // 记录失败
    fun recordFailure() {
        val failures = prefs.getInt(KEY_CONSECUTIVE_FAILURES, 0) + 1
        prefs.edit().putInt(KEY_CONSECUTIVE_FAILURES, failures).apply()
        
        // 连续失败后提高检测级别
        adjustDetectionLevel(true)
    }
    
    // 获取连续失败次数
    fun getConsecutiveFailures(): Int {
        return prefs.getInt(KEY_CONSECUTIVE_FAILURES, 0)
    }
    
    // 自动调整检测级别
    private fun adjustDetectionLevel(isFailure: Boolean) {
        val currentLevel = getDetectionLevel()
        val failures = getConsecutiveFailures()
        
        val newLevel = when {
            isFailure && failures >= 5 -> DetectionLevel.HIGH
            isFailure && failures >= 3 -> DetectionLevel.MEDIUM
            !isFailure && failures == 0 -> DetectionLevel.LOW
            else -> currentLevel
        }
        
        if (newLevel != currentLevel) {
            setDetectionLevel(newLevel)
        }
    }
    
    // 获取策略模式
    fun getStrategyMode(): StrategyMode {
        val mode = prefs.getString(KEY_STRATEGY_MODE, StrategyMode.NORMAL.name)
        return StrategyMode.valueOf(mode ?: StrategyMode.NORMAL.name)
    }
    
    // 设置策略模式
    fun setStrategyMode(mode: StrategyMode) {
        prefs.edit().putString(KEY_STRATEGY_MODE, mode.name).apply()
    }
    
    // 根据当前配置获取延迟时间
    fun getAdaptiveDelay(): Long {
        val baseDelay = getBaseDelay()
        val level = getDetectionLevel()
        val mode = getStrategyMode()
        
        val multiplier = when (level) {
            DetectionLevel.LOW -> 1.0
            DetectionLevel.MEDIUM -> 1.5
            DetectionLevel.HIGH -> 2.0
        }
        
        val modeMultiplier = when (mode) {
            StrategyMode.NORMAL -> 1.0
            StrategyMode.STEALTH -> 2.0
            StrategyMode.AGGRESSIVE -> 0.5
        }
        
        val finalDelay = (baseDelay * multiplier * modeMultiplier).toLong()
        val jitter = Random.nextLong(0, finalDelay / 4) // 25%的随机抖动
        
        return finalDelay + jitter
    }
    
    // 获取适应性重试次数
    fun getAdaptiveRetryCount(): Int {
        val baseRetry = getRetryCount()
        val level = getDetectionLevel()
        
        return when (level) {
            DetectionLevel.LOW -> baseRetry
            DetectionLevel.MEDIUM -> baseRetry + 1
            DetectionLevel.HIGH -> baseRetry + 2
        }
    }
    
    // 是否应该使用高级反检测
    fun shouldUseAdvancedAntiDetection(): Boolean {
        return getDetectionLevel() != DetectionLevel.LOW
    }
    
    // 是否应该切换策略
    fun shouldSwitchStrategy(): Boolean {
        val failures = getConsecutiveFailures()
        val lastSuccess = prefs.getLong(KEY_LAST_SUCCESS_TIME, 0)
        val timeSinceSuccess = System.currentTimeMillis() - lastSuccess
        
        return failures >= 3 || timeSinceSuccess > 30 * 60 * 1000 // 30分钟无成功
    }
    
    // 重置配置
    fun resetConfig() {
        prefs.edit().clear().apply()
    }
    
    // 获取配置摘要
    fun getConfigSummary(): String {
        return """
            检测级别: ${getDetectionLevel()}
            策略模式: ${getStrategyMode()}
            重试次数: ${getAdaptiveRetryCount()}
            基础延迟: ${getBaseDelay()}ms
            连续失败: ${getConsecutiveFailures()}次
            User-Agent索引: ${getUserAgentIndex()}
        """.trimIndent()
    }
}
