package com.xue.hongshu.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.xue.hongshu.data.database.Converters;
import com.xue.hongshu.data.entity.Employee;
import com.xue.hongshu.data.entity.EmployeeStatus;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class EmployeeDao_Impl implements EmployeeDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Employee> __insertionAdapterOfEmployee;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<Employee> __deletionAdapterOfEmployee;

  private final EntityDeletionOrUpdateAdapter<Employee> __updateAdapterOfEmployee;

  private final SharedSQLiteStatement __preparedStmtOfDeleteEmployeeById;

  private final SharedSQLiteStatement __preparedStmtOfUpdateEmployeeStatus;

  private final SharedSQLiteStatement __preparedStmtOfUpdateEmployeePostInfo;

  private final SharedSQLiteStatement __preparedStmtOfUpdateEmployeeError;

  public EmployeeDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfEmployee = new EntityInsertionAdapter<Employee>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `employees` (`id`,`name`,`xhsUserId`,`xhsUserName`,`lastPostTime`,`lastPostTitle`,`lastCheckTime`,`status`,`errorMessage`,`checkCount`,`createdTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Employee entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getXhsUserId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getXhsUserId());
        }
        if (entity.getXhsUserName() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getXhsUserName());
        }
        statement.bindLong(5, entity.getLastPostTime());
        if (entity.getLastPostTitle() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getLastPostTitle());
        }
        statement.bindLong(7, entity.getLastCheckTime());
        final String _tmp = __converters.fromEmployeeStatus(entity.getStatus());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        if (entity.getErrorMessage() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getErrorMessage());
        }
        statement.bindLong(10, entity.getCheckCount());
        statement.bindLong(11, entity.getCreatedTime());
      }
    };
    this.__deletionAdapterOfEmployee = new EntityDeletionOrUpdateAdapter<Employee>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `employees` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Employee entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfEmployee = new EntityDeletionOrUpdateAdapter<Employee>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `employees` SET `id` = ?,`name` = ?,`xhsUserId` = ?,`xhsUserName` = ?,`lastPostTime` = ?,`lastPostTitle` = ?,`lastCheckTime` = ?,`status` = ?,`errorMessage` = ?,`checkCount` = ?,`createdTime` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Employee entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getXhsUserId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getXhsUserId());
        }
        if (entity.getXhsUserName() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getXhsUserName());
        }
        statement.bindLong(5, entity.getLastPostTime());
        if (entity.getLastPostTitle() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getLastPostTitle());
        }
        statement.bindLong(7, entity.getLastCheckTime());
        final String _tmp = __converters.fromEmployeeStatus(entity.getStatus());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        if (entity.getErrorMessage() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getErrorMessage());
        }
        statement.bindLong(10, entity.getCheckCount());
        statement.bindLong(11, entity.getCreatedTime());
        if (entity.getId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteEmployeeById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM employees WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateEmployeeStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE employees SET status = ?, lastCheckTime = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateEmployeePostInfo = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE employees SET lastPostTime = ?, lastPostTitle = ?, status = ?, lastCheckTime = ?, checkCount = checkCount + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateEmployeeError = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE employees SET errorMessage = ?, status = ?, lastCheckTime = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertEmployee(final Employee employee,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfEmployee.insert(employee);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertEmployees(final List<Employee> employees,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfEmployee.insert(employees);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEmployee(final Employee employee,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfEmployee.handle(employee);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateEmployee(final Employee employee,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfEmployee.handle(employee);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEmployeeById(final String id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteEmployeeById.acquire();
        int _argIndex = 1;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteEmployeeById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateEmployeeStatus(final String id, final EmployeeStatus status,
      final long checkTime, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateEmployeeStatus.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromEmployeeStatus(status);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, checkTime);
        _argIndex = 3;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateEmployeeStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateEmployeePostInfo(final String id, final long postTime, final String title,
      final EmployeeStatus status, final long checkTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateEmployeePostInfo.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, postTime);
        _argIndex = 2;
        if (title == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, title);
        }
        _argIndex = 3;
        final String _tmp = __converters.fromEmployeeStatus(status);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 4;
        _stmt.bindLong(_argIndex, checkTime);
        _argIndex = 5;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateEmployeePostInfo.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateEmployeeError(final String id, final String errorMessage,
      final EmployeeStatus status, final long checkTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateEmployeeError.acquire();
        int _argIndex = 1;
        if (errorMessage == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, errorMessage);
        }
        _argIndex = 2;
        final String _tmp = __converters.fromEmployeeStatus(status);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, checkTime);
        _argIndex = 4;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateEmployeeError.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Employee>> getAllEmployees() {
    final String _sql = "SELECT * FROM employees ORDER BY lastPostTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"employees"}, new Callable<List<Employee>>() {
      @Override
      @NonNull
      public List<Employee> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfXhsUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "xhsUserId");
          final int _cursorIndexOfXhsUserName = CursorUtil.getColumnIndexOrThrow(_cursor, "xhsUserName");
          final int _cursorIndexOfLastPostTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPostTime");
          final int _cursorIndexOfLastPostTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPostTitle");
          final int _cursorIndexOfLastCheckTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastCheckTime");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCheckCount = CursorUtil.getColumnIndexOrThrow(_cursor, "checkCount");
          final int _cursorIndexOfCreatedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createdTime");
          final List<Employee> _result = new ArrayList<Employee>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Employee _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpXhsUserId;
            if (_cursor.isNull(_cursorIndexOfXhsUserId)) {
              _tmpXhsUserId = null;
            } else {
              _tmpXhsUserId = _cursor.getString(_cursorIndexOfXhsUserId);
            }
            final String _tmpXhsUserName;
            if (_cursor.isNull(_cursorIndexOfXhsUserName)) {
              _tmpXhsUserName = null;
            } else {
              _tmpXhsUserName = _cursor.getString(_cursorIndexOfXhsUserName);
            }
            final long _tmpLastPostTime;
            _tmpLastPostTime = _cursor.getLong(_cursorIndexOfLastPostTime);
            final String _tmpLastPostTitle;
            if (_cursor.isNull(_cursorIndexOfLastPostTitle)) {
              _tmpLastPostTitle = null;
            } else {
              _tmpLastPostTitle = _cursor.getString(_cursorIndexOfLastPostTitle);
            }
            final long _tmpLastCheckTime;
            _tmpLastCheckTime = _cursor.getLong(_cursorIndexOfLastCheckTime);
            final EmployeeStatus _tmpStatus;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toEmployeeStatus(_tmp);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpCheckCount;
            _tmpCheckCount = _cursor.getInt(_cursorIndexOfCheckCount);
            final long _tmpCreatedTime;
            _tmpCreatedTime = _cursor.getLong(_cursorIndexOfCreatedTime);
            _item = new Employee(_tmpId,_tmpName,_tmpXhsUserId,_tmpXhsUserName,_tmpLastPostTime,_tmpLastPostTitle,_tmpLastCheckTime,_tmpStatus,_tmpErrorMessage,_tmpCheckCount,_tmpCreatedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getEmployeeById(final String id, final Continuation<? super Employee> $completion) {
    final String _sql = "SELECT * FROM employees WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (id == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, id);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Employee>() {
      @Override
      @Nullable
      public Employee call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfXhsUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "xhsUserId");
          final int _cursorIndexOfXhsUserName = CursorUtil.getColumnIndexOrThrow(_cursor, "xhsUserName");
          final int _cursorIndexOfLastPostTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPostTime");
          final int _cursorIndexOfLastPostTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPostTitle");
          final int _cursorIndexOfLastCheckTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastCheckTime");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCheckCount = CursorUtil.getColumnIndexOrThrow(_cursor, "checkCount");
          final int _cursorIndexOfCreatedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createdTime");
          final Employee _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpXhsUserId;
            if (_cursor.isNull(_cursorIndexOfXhsUserId)) {
              _tmpXhsUserId = null;
            } else {
              _tmpXhsUserId = _cursor.getString(_cursorIndexOfXhsUserId);
            }
            final String _tmpXhsUserName;
            if (_cursor.isNull(_cursorIndexOfXhsUserName)) {
              _tmpXhsUserName = null;
            } else {
              _tmpXhsUserName = _cursor.getString(_cursorIndexOfXhsUserName);
            }
            final long _tmpLastPostTime;
            _tmpLastPostTime = _cursor.getLong(_cursorIndexOfLastPostTime);
            final String _tmpLastPostTitle;
            if (_cursor.isNull(_cursorIndexOfLastPostTitle)) {
              _tmpLastPostTitle = null;
            } else {
              _tmpLastPostTitle = _cursor.getString(_cursorIndexOfLastPostTitle);
            }
            final long _tmpLastCheckTime;
            _tmpLastCheckTime = _cursor.getLong(_cursorIndexOfLastCheckTime);
            final EmployeeStatus _tmpStatus;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toEmployeeStatus(_tmp);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpCheckCount;
            _tmpCheckCount = _cursor.getInt(_cursorIndexOfCheckCount);
            final long _tmpCreatedTime;
            _tmpCreatedTime = _cursor.getLong(_cursorIndexOfCreatedTime);
            _result = new Employee(_tmpId,_tmpName,_tmpXhsUserId,_tmpXhsUserName,_tmpLastPostTime,_tmpLastPostTitle,_tmpLastCheckTime,_tmpStatus,_tmpErrorMessage,_tmpCheckCount,_tmpCreatedTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Employee>> getEmployeesByStatus(final EmployeeStatus status) {
    final String _sql = "SELECT * FROM employees WHERE status = ? ORDER BY lastPostTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromEmployeeStatus(status);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"employees"}, new Callable<List<Employee>>() {
      @Override
      @NonNull
      public List<Employee> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfXhsUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "xhsUserId");
          final int _cursorIndexOfXhsUserName = CursorUtil.getColumnIndexOrThrow(_cursor, "xhsUserName");
          final int _cursorIndexOfLastPostTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPostTime");
          final int _cursorIndexOfLastPostTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPostTitle");
          final int _cursorIndexOfLastCheckTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastCheckTime");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCheckCount = CursorUtil.getColumnIndexOrThrow(_cursor, "checkCount");
          final int _cursorIndexOfCreatedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createdTime");
          final List<Employee> _result = new ArrayList<Employee>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Employee _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpXhsUserId;
            if (_cursor.isNull(_cursorIndexOfXhsUserId)) {
              _tmpXhsUserId = null;
            } else {
              _tmpXhsUserId = _cursor.getString(_cursorIndexOfXhsUserId);
            }
            final String _tmpXhsUserName;
            if (_cursor.isNull(_cursorIndexOfXhsUserName)) {
              _tmpXhsUserName = null;
            } else {
              _tmpXhsUserName = _cursor.getString(_cursorIndexOfXhsUserName);
            }
            final long _tmpLastPostTime;
            _tmpLastPostTime = _cursor.getLong(_cursorIndexOfLastPostTime);
            final String _tmpLastPostTitle;
            if (_cursor.isNull(_cursorIndexOfLastPostTitle)) {
              _tmpLastPostTitle = null;
            } else {
              _tmpLastPostTitle = _cursor.getString(_cursorIndexOfLastPostTitle);
            }
            final long _tmpLastCheckTime;
            _tmpLastCheckTime = _cursor.getLong(_cursorIndexOfLastCheckTime);
            final EmployeeStatus _tmpStatus;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toEmployeeStatus(_tmp_1);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpCheckCount;
            _tmpCheckCount = _cursor.getInt(_cursorIndexOfCheckCount);
            final long _tmpCreatedTime;
            _tmpCreatedTime = _cursor.getLong(_cursorIndexOfCreatedTime);
            _item = new Employee(_tmpId,_tmpName,_tmpXhsUserId,_tmpXhsUserName,_tmpLastPostTime,_tmpLastPostTitle,_tmpLastCheckTime,_tmpStatus,_tmpErrorMessage,_tmpCheckCount,_tmpCreatedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Employee>> getOverdueEmployees(final long threshold) {
    final String _sql = "SELECT * FROM employees WHERE lastPostTime < ? ORDER BY lastPostTime ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, threshold);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"employees"}, new Callable<List<Employee>>() {
      @Override
      @NonNull
      public List<Employee> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfXhsUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "xhsUserId");
          final int _cursorIndexOfXhsUserName = CursorUtil.getColumnIndexOrThrow(_cursor, "xhsUserName");
          final int _cursorIndexOfLastPostTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPostTime");
          final int _cursorIndexOfLastPostTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPostTitle");
          final int _cursorIndexOfLastCheckTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastCheckTime");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCheckCount = CursorUtil.getColumnIndexOrThrow(_cursor, "checkCount");
          final int _cursorIndexOfCreatedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createdTime");
          final List<Employee> _result = new ArrayList<Employee>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Employee _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpXhsUserId;
            if (_cursor.isNull(_cursorIndexOfXhsUserId)) {
              _tmpXhsUserId = null;
            } else {
              _tmpXhsUserId = _cursor.getString(_cursorIndexOfXhsUserId);
            }
            final String _tmpXhsUserName;
            if (_cursor.isNull(_cursorIndexOfXhsUserName)) {
              _tmpXhsUserName = null;
            } else {
              _tmpXhsUserName = _cursor.getString(_cursorIndexOfXhsUserName);
            }
            final long _tmpLastPostTime;
            _tmpLastPostTime = _cursor.getLong(_cursorIndexOfLastPostTime);
            final String _tmpLastPostTitle;
            if (_cursor.isNull(_cursorIndexOfLastPostTitle)) {
              _tmpLastPostTitle = null;
            } else {
              _tmpLastPostTitle = _cursor.getString(_cursorIndexOfLastPostTitle);
            }
            final long _tmpLastCheckTime;
            _tmpLastCheckTime = _cursor.getLong(_cursorIndexOfLastCheckTime);
            final EmployeeStatus _tmpStatus;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toEmployeeStatus(_tmp);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpCheckCount;
            _tmpCheckCount = _cursor.getInt(_cursorIndexOfCheckCount);
            final long _tmpCreatedTime;
            _tmpCreatedTime = _cursor.getLong(_cursorIndexOfCreatedTime);
            _item = new Employee(_tmpId,_tmpName,_tmpXhsUserId,_tmpXhsUserName,_tmpLastPostTime,_tmpLastPostTitle,_tmpLastCheckTime,_tmpStatus,_tmpErrorMessage,_tmpCheckCount,_tmpCreatedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getEmployeeCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM employees";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getEmployeeCountByStatus(final EmployeeStatus status,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM employees WHERE status = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromEmployeeStatus(status);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp_1;
            if (_cursor.isNull(0)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(0);
            }
            _result = _tmp_1;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
