package com.xue.hongshu.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class NetworkMonitor(private val context: Context) {
    
    companion object {
        private const val TAG = "NetworkMonitor"
    }
    
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    private val _networkState = MutableStateFlow(NetworkState.UNKNOWN)
    val networkState: StateFlow<NetworkState> = _networkState.asStateFlow()
    
    private val _connectionQuality = MutableStateFlow(ConnectionQuality.UNKNOWN)
    val connectionQuality: StateFlow<ConnectionQuality> = _connectionQuality.asStateFlow()
    
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            Log.d(TAG, "Network available: $network")
            updateNetworkState()
        }
        
        override fun onLost(network: Network) {
            super.onLost(network)
            Log.d(TAG, "Network lost: $network")
            _networkState.value = NetworkState.DISCONNECTED
            _connectionQuality.value = ConnectionQuality.NONE
        }
        
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            Log.d(TAG, "Network capabilities changed: $networkCapabilities")
            updateNetworkState()
            updateConnectionQuality(networkCapabilities)
        }
    }
    
    fun startMonitoring() {
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
        updateNetworkState()
    }
    
    fun stopMonitoring() {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }
    
    private fun updateNetworkState() {
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        _networkState.value = when {
            networkCapabilities == null -> NetworkState.DISCONNECTED
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkState.WIFI
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkState.CELLULAR
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkState.ETHERNET
            else -> NetworkState.OTHER
        }
    }
    
    private fun updateConnectionQuality(networkCapabilities: NetworkCapabilities) {
        val downstreamBandwidth = networkCapabilities.linkDownstreamBandwidthKbps
        val upstreamBandwidth = networkCapabilities.linkUpstreamBandwidthKbps
        
        _connectionQuality.value = when {
            downstreamBandwidth >= 10000 && upstreamBandwidth >= 1000 -> ConnectionQuality.EXCELLENT
            downstreamBandwidth >= 5000 && upstreamBandwidth >= 500 -> ConnectionQuality.GOOD
            downstreamBandwidth >= 1000 && upstreamBandwidth >= 100 -> ConnectionQuality.FAIR
            downstreamBandwidth > 0 -> ConnectionQuality.POOR
            else -> ConnectionQuality.NONE
        }
        
        Log.d(TAG, "Connection quality: ${_connectionQuality.value} (down: ${downstreamBandwidth}kbps, up: ${upstreamBandwidth}kbps)")
    }
    
    fun isNetworkAvailable(): Boolean {
        val activeNetwork = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false
        return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
    
    fun getNetworkType(): String {
        return when (_networkState.value) {
            NetworkState.WIFI -> "WiFi"
            NetworkState.CELLULAR -> "移动网络"
            NetworkState.ETHERNET -> "以太网"
            NetworkState.OTHER -> "其他"
            NetworkState.DISCONNECTED -> "未连接"
            NetworkState.UNKNOWN -> "未知"
        }
    }
    
    fun shouldRetryOnNetwork(): Boolean {
        return when (_connectionQuality.value) {
            ConnectionQuality.EXCELLENT, ConnectionQuality.GOOD -> true
            ConnectionQuality.FAIR -> _networkState.value == NetworkState.WIFI
            ConnectionQuality.POOR, ConnectionQuality.NONE -> false
            ConnectionQuality.UNKNOWN -> isNetworkAvailable()
        }
    }
    
    fun getOptimalTimeout(): Long {
        return when (_connectionQuality.value) {
            ConnectionQuality.EXCELLENT -> 10000L // 10秒
            ConnectionQuality.GOOD -> 15000L      // 15秒
            ConnectionQuality.FAIR -> 20000L      // 20秒
            ConnectionQuality.POOR -> 30000L      // 30秒
            ConnectionQuality.NONE, ConnectionQuality.UNKNOWN -> 45000L // 45秒
        }
    }
}

enum class NetworkState {
    WIFI,
    CELLULAR,
    ETHERNET,
    OTHER,
    DISCONNECTED,
    UNKNOWN
}

enum class ConnectionQuality {
    EXCELLENT,  // 优秀 (>10Mbps down, >1Mbps up)
    GOOD,       // 良好 (>5Mbps down, >500Kbps up)
    FAIR,       // 一般 (>1Mbps down, >100Kbps up)
    POOR,       // 较差 (>0 but low bandwidth)
    NONE,       // 无连接
    UNKNOWN     // 未知
}
