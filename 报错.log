--------- beginning of system
--------- beginning of main
2025-08-08 00:23:32.585  4610-4654  ProfileInstaller        com.xue.hongshu                      D  Installing profile for com.xue.hongshu
2025-08-08 00:23:43.002  4610-4610  UserAction              com.xue.hongshu                      I  用户操作: 启动WebView检查 - 员工ID: fb69f122-84a9-43ba-93f6-6de78f6c2fb8
2025-08-08 00:23:43.011  4610-4610  UserAction              com.xue.hongshu                      I  用户操作: 启动WebView - 员工: 薛博文, 小红书ID: 64d1afd0000000000b007f59
2025-08-08 00:23:43.028  4610-4610  ActivityThread          com.xue.hongshu                      W  handleWindowVisibility: no activity for token android.os.BinderProxy@f7288b1
2025-08-08 00:23:43.073  4610-4610  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/view/RenderNode;->getScaleX()F (dark greylist, linking)
2025-08-08 00:23:43.094  4610-4610  WebViewFactory          com.xue.hongshu                      I  Loading com.android.webview version 91.0.4472.114 (code 447211456)
2025-08-08 00:23:43.103  4610-4610  com.xue.hongsh          com.xue.hongshu                      I  The ClassLoaderContext is a special shared library.
2025-08-08 00:23:43.142  4610-4610  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/os/Trace;->traceBegin(JLjava/lang/String;)V (light greylist, reflection)
2025-08-08 00:23:43.142  4610-4610  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/os/Trace;->traceEnd(J)V (light greylist, reflection)
2025-08-08 00:23:43.178  4610-4610  cr_LibraryLoader        com.xue.hongshu                      I  Loaded native library version number "91.0.4472.114"
2025-08-08 00:23:43.179  4610-4610  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden field Ljava/util/Collections$SynchronizedCollection;->mutex:Ljava/lang/Object; (dark greylist, reflection)
2025-08-08 00:23:43.179  4610-4610  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden field Ljava/util/Collections$SynchronizedCollection;->c:Ljava/util/Collection; (light greylist, reflection)
2025-08-08 00:23:43.179  4610-4610  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Ljava/util/Collections$SynchronizedSet;-><init>(Ljava/util/Set;Ljava/lang/Object;)V (dark greylist, reflection)
2025-08-08 00:23:43.179  4610-4610  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Ljava/util/Collections$SynchronizedCollection;-><init>(Ljava/util/Collection;Ljava/lang/Object;)V (dark greylist, reflection)
2025-08-08 00:23:43.180  4610-4610  cr_CachingUmaRecorder   com.xue.hongshu                      I  Flushed 6 samples from 6 histograms.
2025-08-08 00:23:43.229  4610-4665  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/content/Context;->bindServiceAsUser(Landroid/content/Intent;Landroid/content/ServiceConnection;ILandroid/os/Handler;Landroid/os/UserHandle;)Z (light greylist, reflection)
2025-08-08 00:23:43.521  4610-4688  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/media/AudioManager;->getOutputLatency(I)I (light greylist, reflection)
2025-08-08 00:23:43.546  4610-4698  <no-tag>                com.xue.hongshu                      I  fastpipe: Connect success
2025-08-08 00:23:43.546  4610-4698  HostConnection          com.xue.hongshu                      D  HostRPC::connect sucess: app=com.xue.hongshu, pid=4610, tid=4698, this=0x76388bfc2680
2025-08-08 00:23:43.547  4610-4698  HostConnection          com.xue.hongshu                      D  queryAndSetGLESMaxVersion select gles-version: 3.1 hostGLVersion:46 process:com.xue.hongshu
2025-08-08 00:23:43.551  4610-4688  cr_media                com.xue.hongshu                      W  Requires BLUETOOTH permission
2025-08-08 00:23:43.578  4610-4698  EGL_emulation           com.xue.hongshu                      D  eglCreateContext: 0x76388be640c0: maj 3 min 1 rcv 4
2025-08-08 00:23:43.582  4610-4698  HostConnection          com.xue.hongshu                      D  ExtendedRCEncoderContext GL_VERSION return OpenGL ES 3.1 v1
2025-08-08 00:23:43.588  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000087fe
2025-08-08 00:23:43.621  4610-4640  EGL_emulation           com.xue.hongshu                      E  tid 4640: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:23:43.621  4610-4640  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x7638793b9000, error=EGL_BAD_MATCH
2025-08-08 00:23:43.624  4610-4698  EGL_emulation           com.xue.hongshu                      D  eglCreateContext: 0x76388be640c0: maj 3 min 0 rcv 3
2025-08-08 00:23:43.645  4610-4698  HostConnection          com.xue.hongshu                      D  ExtendedRCEncoderContext GL_VERSION return OpenGL ES 3.1 v1
2025-08-08 00:23:43.649  4610-4610  Choreographer           com.xue.hongshu                      I  Skipped 35 frames!  The application may be doing too much work on its main thread.
2025-08-08 00:23:43.651  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.656  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000087fe
2025-08-08 00:23:43.657  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.657  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.662  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.662  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.686  4610-4640  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.690  4610-4640  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000087fe
2025-08-08 00:23:43.691  4610-4640  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.691  4610-4640  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.834  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(73)] "Enhanced anti-detection script loaded", source:  (73)
2025-08-08 00:23:43.837  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:43.866  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(30)] "Bypass detection script loaded", source:  (30)
2025-08-08 00:23:44.027  4610-4681  NetworkSecurityConfig   com.xue.hongshu                      D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-08 00:23:44.265  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Page started loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:23:44.335  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(0)] "Mixed Content: The page at 'https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59' was loaded over HTTPS, but requested an insecure image 'http://sns-avatar-qc.xhscdn.com/user_banner/1040g2k031i1bh3qin0dg5p6hlv82ovqp82bj5eo?imageView2/2/w/540/format/jpg'. This content should also be served over HTTPS.", source: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59 (0)
2025-08-08 00:23:44.552  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "框架和 SDK 输出的日志默认不展示，可在框架配置文件中设置开启，详见 https://doc.weixin.qq.com/doc/w3_AWkASAb9APAr8IdJI5VS0OyqetUE6?scode=ANAAyQcbAAgB8qKjm9AWkASAb9APA", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:44.733  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-08 00:23:44.806  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:44.827  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 2 lines
2025-08-08 00:23:44.835  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:44.838  4610-4610  chromium                com.xue.hongshu                      E  [ERROR:web_contents_delegate.cc(225)] WebContentsDelegate::CheckMediaAccessPermission: Not supported.
2025-08-08 00:23:44.838  4610-4610  chromium                com.xue.hongshu                      E  [ERROR:web_contents_delegate.cc(225)] WebContentsDelegate::CheckMediaAccessPermission: Not supported.
2025-08-08 00:23:44.848  4610-4701  CameraManagerGlobal     com.xue.hongshu                      I  Connecting to camera service
2025-08-08 00:23:44.860  4610-4688  cr_media                com.xue.hongshu                      W  Requires MODIFY_AUDIO_SETTINGS and RECORD_AUDIO. No audio device will be available for recording
2025-08-08 00:23:44.869  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:23:44.869  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Waiting 3489ms before extracting data
2025-08-08 00:23:44.906  4610-4701  CameraBase              com.xue.hongshu                      W  An error occurred while connecting to camera 1: Status(-8): '1: validateClientPermissionsLocked:906: Caller "com.xue.hongshu" (PID 10052, UID 4610) cannot open camera "1" without camera permission'
2025-08-08 00:23:44.908  4610-4701  cr_VideoCapture         com.xue.hongshu                      E  Camera.open:  (Ask Gemini)
                                                                                                    java.lang.RuntimeException: Fail to connect to camera service
                                                                                                    	at android.hardware.Camera.<init>(Camera.java:546)
                                                                                                    	at android.hardware.Camera.open(Camera.java:392)
                                                                                                    	at org.chromium.media.VideoCaptureFactory.isZoomSupported(chromium-SystemWebView.apk-default-447211456:3)
2025-08-08 00:23:44.936  4610-4701  CameraBase              com.xue.hongshu                      W  An error occurred while connecting to camera 1: Status(-8): '1: validateClientPermissionsLocked:906: Caller "com.xue.hongshu" (PID 10052, UID 4610) cannot open camera "1" without camera permission'
2025-08-08 00:23:44.937  4610-4701  cr_VideoCapture         com.xue.hongshu                      E  Camera.open:  (Ask Gemini)
                                                                                                    java.lang.RuntimeException: Fail to connect to camera service
                                                                                                    	at android.hardware.Camera.<init>(Camera.java:546)
                                                                                                    	at android.hardware.Camera.open(Camera.java:392)
                                                                                                    	at org.chromium.media.VideoCaptureFactory.getDeviceSupportedFormats(chromium-SystemWebView.apk-default-447211456:3)
2025-08-08 00:23:44.985  4610-4701  CameraBase              com.xue.hongshu                      W  An error occurred while connecting to camera 0: Status(-8): '1: validateClientPermissionsLocked:906: Caller "com.xue.hongshu" (PID 10052, UID 4610) cannot open camera "0" without camera permission'
2025-08-08 00:23:44.986  4610-4701  cr_VideoCapture         com.xue.hongshu                      E  Camera.open:  (Ask Gemini)
                                                                                                    java.lang.RuntimeException: Fail to connect to camera service
                                                                                                    	at android.hardware.Camera.<init>(Camera.java:546)
                                                                                                    	at android.hardware.Camera.open(Camera.java:392)
                                                                                                    	at org.chromium.media.VideoCaptureFactory.isZoomSupported(chromium-SystemWebView.apk-default-447211456:3)
2025-08-08 00:23:45.001  4610-4701  CameraBase              com.xue.hongshu                      W  An error occurred while connecting to camera 0: Status(-8): '1: validateClientPermissionsLocked:906: Caller "com.xue.hongshu" (PID 10052, UID 4610) cannot open camera "0" without camera permission'
2025-08-08 00:23:45.002  4610-4701  cr_VideoCapture         com.xue.hongshu                      E  Camera.open:  (Ask Gemini)
                                                                                                    java.lang.RuntimeException: Fail to connect to camera service
                                                                                                    	at android.hardware.Camera.<init>(Camera.java:546)
                                                                                                    	at android.hardware.Camera.open(Camera.java:392)
                                                                                                    	at org.chromium.media.VideoCaptureFactory.getDeviceSupportedFormats(chromium-SystemWebView.apk-default-447211456:3)
2025-08-08 00:23:45.305  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:23:45.306  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Waiting 4589ms before extracting data
2025-08-08 00:23:45.403  4610-4640  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:23:45.439  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:45.480  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-08 00:23:45.744  4610-4688  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:23:45.744  4610-4688  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:23:45.744  4610-4688  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:23:45.747  4610-4688  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:23:45.884  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:45.898  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:45.901  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.114  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:23:46.120  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.140  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:23:46.140  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Waiting 2955ms before extracting data
2025-08-08 00:23:46.163  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.220  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 5 lines
2025-08-08 00:23:46.222  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.226  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:23:46.227  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:23:46.228  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.239  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:23:46.242  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.254  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.257  4610-4688  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:23:46.257  4610-4688  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:23:46.257  4610-4688  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:23:46.258  4610-4688  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:23:46.272  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.275  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 1 line
2025-08-08 00:23:46.281  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.297  4610-4698  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:23:46.307  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.308  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 1 line
2025-08-08 00:23:46.313  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.315  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:23:46.317  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:23:46.318  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.328  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:23:46.331  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.391  4610-4688  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:23:46.391  4610-4688  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:23:46.391  4610-4688  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:23:46.392  4610-4688  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:23:46.393  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.466  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 5 lines
2025-08-08 00:23:46.469  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.471  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:23:46.474  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:23:46.475  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.487  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:23:46.490  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] pageView", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] visitMobile", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] AbTestPluginMetrics", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] AbTestPluginMetrics", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.496  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.504  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.504  4610-4688  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:23:46.504  4610-4688  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:23:46.504  4610-4688  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:23:46.505  4610-4688  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:23:46.517  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.518  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 1 line
2025-08-08 00:23:46.528  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.546  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.555  4610-4698  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:23:46.565  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.566  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 1 line
2025-08-08 00:23:46.570  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.578  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:23:46.581  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:23:46.581  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.610  4610-4698  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:23:46.614  4610-4698  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.617  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.618  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.619  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:46.620  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:48.358  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:23:48.358  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:23:49.010  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserMemory", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.010  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.010  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserNetwork", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.010  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.010  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserNavigationTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.010  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.010  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserScriptsExecutionTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.011  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.011  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserRenderTimes", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.011  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.014  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:23:49.014  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:23:49.014  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:23:49.014  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:23:49.014  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(21)] "尝试触发懒加载...", source:  (21)
2025-08-08 00:23:49.015  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (65)
2025-08-08 00:23:49.015  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .user-name found 1 elements", source:  (69)
2025-08-08 00:23:49.015  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(74)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (74)
2025-08-08 00:23:49.016  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(85)] "Starting enhanced time search...", source:  (85)
2025-08-08 00:23:49.016  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (65)
2025-08-08 00:23:49.016  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .time-text found 0 elements", source:  (69)
2025-08-08 00:23:49.016  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-time found 0 elements", source:  (69)
2025-08-08 00:23:49.017  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-time found 0 elements", source:  (69)
2025-08-08 00:23:49.017  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .create-time found 0 elements", source:  (69)
2025-08-08 00:23:49.017  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [data-time] found 0 elements", source:  (69)
2025-08-08 00:23:49.017  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-time found 0 elements", source:  (69)
2025-08-08 00:23:49.017  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .timestamp found 0 elements", source:  (69)
2025-08-08 00:23:49.017  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector time found 0 elements", source:  (69)
2025-08-08 00:23:49.017  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .date found 0 elements", source:  (69)
2025-08-08 00:23:49.017  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-date found 0 elements", source:  (69)
2025-08-08 00:23:49.018  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .creation-time found 0 elements", source:  (69)
2025-08-08 00:23:49.018  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-date found 0 elements", source:  (69)
2025-08-08 00:23:49.018  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="time"] found 0 elements", source:  (69)
2025-08-08 00:23:49.018  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="date"] found 0 elements", source:  (69)
2025-08-08 00:23:49.018  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="publish"] found 0 elements", source:  (69)
2025-08-08 00:23:49.018  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.018  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .feed-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.019  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.019  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No time found", source:  (79)
2025-08-08 00:23:49.019  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(92)] "Searching for time patterns in text content...", source:  (92)
2025-08-08 00:23:49.021  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(126)] "No time element found with text patterns", source:  (126)
2025-08-08 00:23:49.022  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(264)] "Searching for latest note time...", source:  (264)
2025-08-08 00:23:49.022  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(132)] "=== 开始页面结构分析 ===", source:  (132)
2025-08-08 00:23:49.022  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(144)] "总元素数量: 216", source:  (144)
2025-08-08 00:23:49.024  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(165)] "找到的时间元素: ", source:  (165)
2025-08-08 00:23:49.024  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(190)] "找到的容器数量: 5", source:  (190)
2025-08-08 00:23:49.025  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器1: .note-container (数量: 1, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.025  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: note-container", source:  (193)
2025-08-08 00:23:49.025  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.025  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘开口吉言戒指  哈尔滨金包银老刘1", source:  (195)
2025-08-08 00:23:49.025  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器2: [class*="note"] (数量: 19, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.025  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: note-container", source:  (193)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘开口吉言戒指  哈尔滨金包银老刘1", source:  (195)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器3: [class*="list"] (数量: 1, 子元素: 4)", source:  (192)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: primary left reds-tabs-list", source:  (193)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 商品笔记收藏", source:  (195)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器4: [class*="item"] (数量: 8, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: reds-tab-item", source:  (193)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 商品", source:  (195)
2025-08-08 00:23:49.026  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器5: [class*="card"] (数量: 4, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.027  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: user-card", source:  (193)
2025-08-08 00:23:49.027  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.027  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色10+关注10+粉丝10+获赞与收藏关注", source:  (195)
2025-08-08 00:23:49.027  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(222)] "时间相关class的元素数量: 0", source:  (222)
2025-08-08 00:23:49.027  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(228)] "=== 查找作品链接 ===", source:  (228)
2025-08-08 00:23:49.027  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(252)] "总共找到作品链接: 0", source:  (252)
2025-08-08 00:23:49.028  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for title with selectors: .note-title,.post-title,.title,.content-title,h1,h2,h3,.note-content,.post-content", source:  (65)
2025-08-08 00:23:49.028  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-title found 0 elements", source:  (69)
2025-08-08 00:23:49.028  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-title found 0 elements", source:  (69)
2025-08-08 00:23:49.029  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .title found 0 elements", source:  (69)
2025-08-08 00:23:49.029  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .content-title found 0 elements", source:  (69)
2025-08-08 00:23:49.029  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h1 found 0 elements", source:  (69)
2025-08-08 00:23:49.029  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h2 found 0 elements", source:  (69)
2025-08-08 00:23:49.029  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h3 found 0 elements", source:  (69)
2025-08-08 00:23:49.029  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-content found 0 elements", source:  (69)
2025-08-08 00:23:49.029  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-content found 0 elements", source:  (69)
2025-08-08 00:23:49.029  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No title found", source:  (79)
2025-08-08 00:23:49.030  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(320)] "Page analysis: [object Object]", source:  (320)
2025-08-08 00:23:49.030  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(324)] "=== 页面DOM结构分析 ===", source:  (324)
2025-08-08 00:23:49.030  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(328)] "前20个有class的元素:", source:  (328)
2025-08-08 00:23:49.030  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "1. DIV.main-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.030  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "2. DIV.scroll-view-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.030  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "3. DIV.user - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.031  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "4. DIV.user-bg - """, source:  (330)
2025-08-08 00:23:49.031  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "5. DIV.user-bg--placeholder - """, source:  (330)
2025-08-08 00:23:49.031  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "6. DIV.user-card - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.031  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "7. DIV.launch-app-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.031  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "8. DIV.position-relative - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.031  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "9. DIV.user-card-center - "哈尔滨金包银老刘小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.032  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "10. SPAN.reds-img-container responsive reds-avatar size-l - """, source:  (330)
2025-08-08 00:23:49.032  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "11. SPAN.reds-img-placeholder - """, source:  (330)
2025-08-08 00:23:49.032  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "12. PICTURE.reds-img-box - """, source:  (330)
2025-08-08 00:23:49.032  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "13. IMG.reds-img - """, source:  (330)
2025-08-08 00:23:49.032  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "14. DIV.reds-img-box - """, source:  (330)
2025-08-08 00:23:49.032  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "15. I.reds-avatar-border - """, source:  (330)
2025-08-08 00:23:49.032  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "16. DIV.user-name-box - "哈尔滨金包银老刘小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.032  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "17. DIV.user-name - "哈尔滨金包银老刘"", source:  (330)
2025-08-08 00:23:49.033  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "18. DIV.red-id - "小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.035  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "19. DIV.author-desc-wrapper - "展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色"", source:  (330)
2025-08-08 00:23:49.035  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "20. DIV.author-desc - "展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色"", source:  (330)
2025-08-08 00:23:49.035  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(335)] "所有有id的元素:", source:  (335)
2025-08-08 00:23:49.035  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "1. svg#__SVG_SPRITE_NODE__ - """, source:  (337)
2025-08-08 00:23:49.035  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "2. symbol#note-like - """, source:  (337)
2025-08-08 00:23:49.036  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "3. symbol#note-liked - """, source:  (337)
2025-08-08 00:23:49.036  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "4. symbol#icon-man - """, source:  (337)
2025-08-08 00:23:49.036  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "5. symbol#icon-woman - """, source:  (337)
2025-08-08 00:23:49.036  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "6. symbol#icon-message - """, source:  (337)
2025-08-08 00:23:49.036  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "7. symbol#icon-locked - """, source:  (337)
2025-08-08 00:23:49.036  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "8. symbol#icon-arrow-right - """, source:  (337)
2025-08-08 00:23:49.036  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "9. symbol#video-icon - """, source:  (337)
2025-08-08 00:23:49.037  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "10. filter#video-icon_a - """, source:  (337)
2025-08-08 00:23:49.037  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "11. symbol#icon-collection-item - """, source:  (337)
2025-08-08 00:23:49.037  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "12. linearGradient#icon-collection-item_a - """, source:  (337)
2025-08-08 00:23:49.038  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "13. symbol#icon-empty-note - """, source:  (337)
2025-08-08 00:23:49.038  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "14. DIV#app - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (337)
2025-08-08 00:23:49.038  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "15. SECTION#3c01f085a76d45bca365bbaad8cae721 - "实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘"", source:  (337)
2025-08-08 00:23:49.038  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "16. SECTION#d2fbf0e32b924323b3f9c935c0a75f06 - "开口吉言戒指  哈尔滨金包银老刘1"", source:  (337)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "17. DIV#__XHS_AB_FLAGS__ - """, source:  (337)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(341)] "页面主要结构:", source:  (341)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素1: svg.[object SVGAnimatedString] (子元素数: 10)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素2: DIV. (子元素数: 2)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素3: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素4: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素5: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素6: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素7: DIV. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素8: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素9: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素10: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(357)] "可能的作品容器:", source:  (357)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "section: 找到2个元素", source:  (361)
2025-08-08 00:23:49.039  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: SECTION.reds-note-card note-card (子元素: 3)", source:  (364)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="note"]: 找到8个元素", source:  (361)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.note-container (子元素: 2)", source:  (364)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="list"]: 找到1个元素", source:  (361)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.primary left reds-tabs-list (子元素: 4)", source:  (364)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="item"]: 找到8个元素", source:  (361)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.reds-tab-item (子元素: 2)", source:  (364)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="card"]: 找到2个元素", source:  (361)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.user-card (子元素: 2)", source:  (364)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="content"]: 找到1个元素", source:  (361)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.tab-content (子元素: 1)", source:  (364)
2025-08-08 00:23:49.040  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(411)] "Extraction result: [object Object]", source:  (411)
2025-08-08 00:23:49.040  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:23:49.040  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":true,\"userName\":\"哈尔滨金包银老刘\",\"lastPostTime\":\"\",\"lastPostTitle\":\"\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754583829033,\"debug\":{\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"title\":\"@哈尔滨金包银老刘 的个人主页\",\"bodyClasses\":\"\",\"allElements\":216,\"hasUserInfo\":true,\"hasTimeInfo\":false,\"hasTitleInfo\":false,\"userNameSelector\":\"DIV.user-name\",\"timeSelector\":\"none\",\"titleSelector\":\"none\",\"timeElementText\":\"none\",\"timeElementAttributes\":[]}}"
2025-08-08 00:23:49.043  4610-4610  DataExtraction          com.xue.hongshu                      I  数据抓取: 员工ID=fb69f122-84a9-43ba-93f6-6de78f6c2fb8, 结果=成功, 详情=用户名: 哈尔滨金包银老刘, 时间: 
2025-08-08 00:23:49.059  4610-4610  System.err              com.xue.hongshu                      W  java.lang.Exception: Toast callstack! strTip=数据更新成功
2025-08-08 00:23:49.059  4610-4610  System.err              com.xue.hongshu                      W  用户名: 哈尔滨金包银老刘
2025-08-08 00:23:49.059  4610-4610  System.err              com.xue.hongshu                      W  最新发布: 未获取到时间信息
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at android.widget.Toast.show(Toast.java:143)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.invokeSuspend$lambda$0(WebViewActivity.kt:95)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.$r8$lambda$OaNj8h7GSrZfJk-HuBtkTZRZUMw(Unknown Source:0)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at android.app.Activity.runOnUiThread(Activity.java:6423)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.invokeSuspend(WebViewActivity.kt:93)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at android.os.Handler.handleCallback(Handler.java:873)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at android.os.Handler.dispatchMessage(Handler.java:99)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at android.os.Looper.loop(Looper.java:193)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at android.app.ActivityThread.main(ActivityThread.java:6834)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at java.lang.reflect.Method.invoke(Native Method)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
2025-08-08 00:23:49.060  4610-4610  System.err              com.xue.hongshu                      W  	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-08-08 00:23:49.129  4610-4640  EGL_emulation           com.xue.hongshu                      E  tid 4640: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:23:49.129  4610-4640  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878dd3500, error=EGL_BAD_MATCH
2025-08-08 00:23:49.137  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:23:49.137  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:23:49.164  4610-4640  EGL_emulation           com.xue.hongshu                      E  tid 4640: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:23:49.164  4610-4640  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878dd3c80, error=EGL_BAD_MATCH
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://dhjaibcmgocbpegmejfphjhhpaphmkpp/is.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://beeaddblkeeialcohiolkkoiifhgjooj/manifest.json. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://ddgooekaaihgnbfbgalfiooiicdnmnia/js/js-index.ts.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://keeelahekhhgkpaipdodgjnmgkfcdpde/inject.6070000c.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://dbichmdlbjdeplpkhcejgkakobjbjalc/content-scripts/xiaohongshu.css. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://piejlhcmefdepbgalcongckfomfobokb/worker.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://lenndnnbdichpjlfmfadcjpaenmiflan/inject.bundle.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:23:49.194  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://hmeohemhimcjlegdjloglnkfablbneif/inject.bundle.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(21)] "尝试触发懒加载...", source:  (21)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (65)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .user-name found 1 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(74)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (74)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(85)] "Starting enhanced time search...", source:  (85)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (65)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .time-text found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .create-time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [data-time] found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .timestamp found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .date found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-date found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .creation-time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-date found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="time"] found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="date"] found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="publish"] found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .feed-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No time found", source:  (79)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(92)] "Searching for time patterns in text content...", source:  (92)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(126)] "No time element found with text patterns", source:  (126)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(264)] "Searching for latest note time...", source:  (264)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(132)] "=== 开始页面结构分析 ===", source:  (132)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(144)] "总元素数量: 216", source:  (144)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(165)] "找到的时间元素: ", source:  (165)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(190)] "找到的容器数量: 5", source:  (190)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器1: .note-container (数量: 1, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: note-container", source:  (193)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘开口吉言戒指  哈尔滨金包银老刘1", source:  (195)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器2: [class*="note"] (数量: 19, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: note-container", source:  (193)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘开口吉言戒指  哈尔滨金包银老刘1", source:  (195)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器3: [class*="list"] (数量: 1, 子元素: 4)", source:  (192)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: primary left reds-tabs-list", source:  (193)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 商品笔记收藏", source:  (195)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器4: [class*="item"] (数量: 8, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: reds-tab-item", source:  (193)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 商品", source:  (195)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器5: [class*="card"] (数量: 4, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: user-card", source:  (193)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色10+关注10+粉丝10+获赞与收藏关注", source:  (195)
2025-08-08 00:23:49.206  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(222)] "时间相关class的元素数量: 0", source:  (222)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(228)] "=== 查找作品链接 ===", source:  (228)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(252)] "总共找到作品链接: 0", source:  (252)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for title with selectors: .note-title,.post-title,.title,.content-title,h1,h2,h3,.note-content,.post-content", source:  (65)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-title found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-title found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .title found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .content-title found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h1 found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h2 found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h3 found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-content found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-content found 0 elements", source:  (69)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No title found", source:  (79)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(320)] "Page analysis: [object Object]", source:  (320)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(324)] "=== 页面DOM结构分析 ===", source:  (324)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(328)] "前20个有class的元素:", source:  (328)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "1. DIV.main-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "2. DIV.scroll-view-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "3. DIV.user - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "4. DIV.user-bg - """, source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "5. DIV.user-bg--placeholder - """, source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "6. DIV.user-card - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "7. DIV.launch-app-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "8. DIV.position-relative - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "9. DIV.user-card-center - "哈尔滨金包银老刘小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "10. SPAN.reds-img-container responsive reds-avatar size-l - """, source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "11. SPAN.reds-img-placeholder - """, source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "12. PICTURE.reds-img-box - """, source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "13. IMG.reds-img - """, source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "14. DIV.reds-img-box - """, source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "15. I.reds-avatar-border - """, source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "16. DIV.user-name-box - "哈尔滨金包银老刘小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "17. DIV.user-name - "哈尔滨金包银老刘"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "18. DIV.red-id - "小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "19. DIV.author-desc-wrapper - "展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "20. DIV.author-desc - "展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色"", source:  (330)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(335)] "所有有id的元素:", source:  (335)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "1. svg#__SVG_SPRITE_NODE__ - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "2. symbol#note-like - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "3. symbol#note-liked - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "4. symbol#icon-man - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "5. symbol#icon-woman - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "6. symbol#icon-message - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "7. symbol#icon-locked - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "8. symbol#icon-arrow-right - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "9. symbol#video-icon - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "10. filter#video-icon_a - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "11. symbol#icon-collection-item - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "12. linearGradient#icon-collection-item_a - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "13. symbol#icon-empty-note - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "14. DIV#app - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "15. SECTION#3c01f085a76d45bca365bbaad8cae721 - "实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘"", source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "16. SECTION#d2fbf0e32b924323b3f9c935c0a75f06 - "开口吉言戒指  哈尔滨金包银老刘1"", source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "17. DIV#__XHS_AB_FLAGS__ - """, source:  (337)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(341)] "页面主要结构:", source:  (341)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素1: svg.[object SVGAnimatedString] (子元素数: 10)", source:  (346)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素2: DIV. (子元素数: 2)", source:  (346)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素3: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素4: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素5: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.207  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素6: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素7: DIV. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素8: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素9: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素10: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(357)] "可能的作品容器:", source:  (357)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "section: 找到2个元素", source:  (361)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: SECTION.reds-note-card note-card (子元素: 3)", source:  (364)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="note"]: 找到8个元素", source:  (361)
2025-08-08 00:23:49.208  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.note-container (子元素: 2)", source:  (364)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="list"]: 找到1个元素", source:  (361)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.primary left reds-tabs-list (子元素: 4)", source:  (364)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="item"]: 找到8个元素", source:  (361)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.reds-tab-item (子元素: 2)", source:  (364)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="card"]: 找到2个元素", source:  (361)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.user-card (子元素: 2)", source:  (364)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="content"]: 找到1个元素", source:  (361)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.tab-content (子元素: 1)", source:  (364)
2025-08-08 00:23:49.223  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(411)] "Extraction result: [object Object]", source:  (411)
2025-08-08 00:23:49.228  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Ignoring debug-related error: net::ERR_CONNECTION_REFUSED for http://127.0.0.1:9222/json/version
2025-08-08 00:23:49.228  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Ignoring debug-related error: net::ERR_CONNECTION_REFUSED for http://127.0.0.1:54345/
2025-08-08 00:23:49.230  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:23:49.230  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":true,\"userName\":\"哈尔滨金包银老刘\",\"lastPostTime\":\"\",\"lastPostTitle\":\"\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754583829205,\"debug\":{\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"title\":\"@哈尔滨金包银老刘 的个人主页\",\"bodyClasses\":\"\",\"allElements\":216,\"hasUserInfo\":true,\"hasTimeInfo\":false,\"hasTitleInfo\":false,\"userNameSelector\":\"DIV.user-name\",\"timeSelector\":\"none\",\"titleSelector\":\"none\",\"timeElementText\":\"none\",\"timeElementAttributes\":[]}}"
2025-08-08 00:23:49.231  4610-4610  DataExtraction          com.xue.hongshu                      I  数据抓取: 员工ID=fb69f122-84a9-43ba-93f6-6de78f6c2fb8, 结果=成功, 详情=用户名: 哈尔滨金包银老刘, 时间: 
2025-08-08 00:23:49.238  4610-4610  System.err              com.xue.hongshu                      W  java.lang.Exception: Toast callstack! strTip=数据更新成功
2025-08-08 00:23:49.238  4610-4610  System.err              com.xue.hongshu                      W  用户名: 哈尔滨金包银老刘
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  最新发布: 未获取到时间信息
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at android.widget.Toast.show(Toast.java:143)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.invokeSuspend$lambda$0(WebViewActivity.kt:95)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.$r8$lambda$OaNj8h7GSrZfJk-HuBtkTZRZUMw(Unknown Source:0)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at android.app.Activity.runOnUiThread(Activity.java:6423)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.invokeSuspend(WebViewActivity.kt:93)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at android.os.Handler.handleCallback(Handler.java:873)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at android.os.Handler.dispatchMessage(Handler.java:99)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at android.os.Looper.loop(Looper.java:193)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at android.app.ActivityThread.main(ActivityThread.java:6834)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at java.lang.reflect.Method.invoke(Native Method)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
2025-08-08 00:23:49.239  4610-4610  System.err              com.xue.hongshu                      W  	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-08-08 00:23:49.296  4610-4640  EGL_emulation           com.xue.hongshu                      E  tid 4640: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:23:49.296  4610-4640  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878dd3c00, error=EGL_BAD_MATCH
2025-08-08 00:23:49.326  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(29)] "滚动完成", source:  (29)
2025-08-08 00:23:49.332  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm performance metric] paintTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.332  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.332  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm performance metric] visualStability", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.332  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:49.508  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(29)] "滚动完成", source:  (29)
2025-08-08 00:23:49.898  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:23:49.898  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(21)] "尝试触发懒加载...", source:  (21)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (65)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .user-name found 1 elements", source:  (69)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(74)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (74)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(85)] "Starting enhanced time search...", source:  (85)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (65)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .time-text found 0 elements", source:  (69)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-time found 0 elements", source:  (69)
2025-08-08 00:23:49.900  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-time found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .create-time found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [data-time] found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-time found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .timestamp found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector time found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .date found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-date found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .creation-time found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-date found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="time"] found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="date"] found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="publish"] found 0 elements", source:  (69)
2025-08-08 00:23:49.901  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.902  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .feed-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.902  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-item time found 0 elements", source:  (69)
2025-08-08 00:23:49.902  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No time found", source:  (79)
2025-08-08 00:23:49.902  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(92)] "Searching for time patterns in text content...", source:  (92)
2025-08-08 00:23:49.903  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(126)] "No time element found with text patterns", source:  (126)
2025-08-08 00:23:49.903  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(264)] "Searching for latest note time...", source:  (264)
2025-08-08 00:23:49.903  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(132)] "=== 开始页面结构分析 ===", source:  (132)
2025-08-08 00:23:49.903  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(144)] "总元素数量: 216", source:  (144)
2025-08-08 00:23:49.904  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(165)] "找到的时间元素: ", source:  (165)
2025-08-08 00:23:49.904  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(190)] "找到的容器数量: 5", source:  (190)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器1: .note-container (数量: 1, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: note-container", source:  (193)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘开口吉言戒指  哈尔滨金包银老刘1", source:  (195)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器2: [class*="note"] (数量: 19, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: note-container", source:  (193)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘开口吉言戒指  哈尔滨金包银老刘1", source:  (195)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器3: [class*="list"] (数量: 1, 子元素: 4)", source:  (192)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: primary left reds-tabs-list", source:  (193)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 商品笔记收藏", source:  (195)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器4: [class*="item"] (数量: 8, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: reds-tab-item", source:  (193)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 商品", source:  (195)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "容器5: [class*="card"] (数量: 4, 子元素: 2)", source:  (192)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(193)] "  类名: user-card", source:  (193)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(194)] "  ID: ", source:  (194)
2025-08-08 00:23:49.905  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(195)] "  内容: 哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色10+关注10+粉丝10+获赞与收藏关注", source:  (195)
2025-08-08 00:23:49.906  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(222)] "时间相关class的元素数量: 0", source:  (222)
2025-08-08 00:23:49.906  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(228)] "=== 查找作品链接 ===", source:  (228)
2025-08-08 00:23:49.906  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(252)] "总共找到作品链接: 0", source:  (252)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for title with selectors: .note-title,.post-title,.title,.content-title,h1,h2,h3,.note-content,.post-content", source:  (65)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-title found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-title found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .title found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .content-title found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h1 found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h2 found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector h3 found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-content found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-content found 0 elements", source:  (69)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No title found", source:  (79)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(320)] "Page analysis: [object Object]", source:  (320)
2025-08-08 00:23:49.907  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(324)] "=== 页面DOM结构分析 ===", source:  (324)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(328)] "前20个有class的元素:", source:  (328)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "1. DIV.main-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "2. DIV.scroll-view-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "3. DIV.user - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "4. DIV.user-bg - """, source:  (330)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "5. DIV.user-bg--placeholder - """, source:  (330)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "6. DIV.user-card - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "7. DIV.launch-app-container - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.908  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "8. DIV.position-relative - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "9. DIV.user-card-center - "哈尔滨金包银老刘小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "10. SPAN.reds-img-container responsive reds-avatar size-l - """, source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "11. SPAN.reds-img-placeholder - """, source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "12. PICTURE.reds-img-box - """, source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "13. IMG.reds-img - """, source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "14. DIV.reds-img-box - """, source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "15. I.reds-avatar-border - """, source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "16. DIV.user-name-box - "哈尔滨金包银老刘小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "17. DIV.user-name - "哈尔滨金包银老刘"", source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "18. DIV.red-id - "小红书号 2018664371"", source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "19. DIV.author-desc-wrapper - "展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色"", source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(330)] "20. DIV.author-desc - "展开金包银连锁实体，源头工厂，终身质保999足金包999足银，不漏银，不掉色"", source:  (330)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(335)] "所有有id的元素:", source:  (335)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "1. svg#__SVG_SPRITE_NODE__ - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "2. symbol#note-like - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "3. symbol#note-liked - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "4. symbol#icon-man - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "5. symbol#icon-woman - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "6. symbol#icon-message - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "7. symbol#icon-locked - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "8. symbol#icon-arrow-right - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "9. symbol#video-icon - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "10. filter#video-icon_a - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "11. symbol#icon-collection-item - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "12. linearGradient#icon-collection-item_a - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "13. symbol#icon-empty-note - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "14. DIV#app - "哈尔滨金包银老刘小红书号 2018664371展开金包银连锁实体，源头工厂，终身质保999足金包99"", source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "15. SECTION#3c01f085a76d45bca365bbaad8cae721 - "实拍图丨🔥爆全网的金包银耳钉分享～2克💍 哈尔滨金包银老刘"", source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "16. SECTION#d2fbf0e32b924323b3f9c935c0a75f06 - "开口吉言戒指  哈尔滨金包银老刘1"", source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(337)] "17. DIV#__XHS_AB_FLAGS__ - """, source:  (337)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(341)] "页面主要结构:", source:  (341)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素1: svg.[object SVGAnimatedString] (子元素数: 10)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素2: DIV. (子元素数: 2)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素3: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素4: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素5: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素6: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素7: DIV. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素8: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素9: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(346)] "Body子元素10: SCRIPT. (子元素数: 0)", source:  (346)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(357)] "可能的作品容器:", source:  (357)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "section: 找到2个元素", source:  (361)
2025-08-08 00:23:49.909  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: SECTION.reds-note-card note-card (子元素: 3)", source:  (364)
2025-08-08 00:23:49.910  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="note"]: 找到8个元素", source:  (361)
2025-08-08 00:23:49.910  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.note-container (子元素: 2)", source:  (364)
2025-08-08 00:23:49.910  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="list"]: 找到1个元素", source:  (361)
2025-08-08 00:23:49.910  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.primary left reds-tabs-list (子元素: 4)", source:  (364)
2025-08-08 00:23:49.910  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="item"]: 找到8个元素", source:  (361)
2025-08-08 00:23:49.910  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.reds-tab-item (子元素: 2)", source:  (364)
2025-08-08 00:23:49.910  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="card"]: 找到2个元素", source:  (361)
2025-08-08 00:23:49.910  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.user-card (子元素: 2)", source:  (364)
2025-08-08 00:23:49.911  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(361)] "div[class*="content"]: 找到1个元素", source:  (361)
2025-08-08 00:23:49.911  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(364)] "  第一个元素: DIV.tab-content (子元素: 1)", source:  (364)
2025-08-08 00:23:49.911  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(411)] "Extraction result: [object Object]", source:  (411)
2025-08-08 00:23:49.911  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:23:49.911  4610-4610  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":true,\"userName\":\"哈尔滨金包银老刘\",\"lastPostTime\":\"\",\"lastPostTitle\":\"\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754583829910,\"debug\":{\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"title\":\"@哈尔滨金包银老刘 的个人主页\",\"bodyClasses\":\"\",\"allElements\":216,\"hasUserInfo\":true,\"hasTimeInfo\":false,\"hasTitleInfo\":false,\"userNameSelector\":\"DIV.user-name\",\"timeSelector\":\"none\",\"titleSelector\":\"none\",\"timeElementText\":\"none\",\"timeElementAttributes\":[]}}"
2025-08-08 00:23:50.200  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(29)] "滚动完成", source:  (29)
2025-08-08 00:23:51.533  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:23:51.533  4610-4610  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
