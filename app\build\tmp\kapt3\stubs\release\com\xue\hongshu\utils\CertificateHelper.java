package com.xue.hongshu.utils;

/**
 * SSL证书辅助工具
 * 用于获取和分析SSL证书信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0002\b\u0005\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0003\u001e\u001f B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0005\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u0004J\u0010\u0010\t\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0006\u001a\u00020\u0007J\u0014\u0010\n\u001a\u00020\u00042\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00070\fJ\u001a\u0010\r\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u000e\u001a\u00020\u00042\b\b\u0002\u0010\u000f\u001a\u00020\u0010J\u000e\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0006\u001a\u00020\u0007J\u0016\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\u0006\u0010\u0006\u001a\u00020\u0007H\u0002J\u0010\u0010\u0014\u001a\u00020\u00102\u0006\u0010\u0015\u001a\u00020\u0016H\u0002J\u0016\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\u0006\u0010\u0006\u001a\u00020\u0007H\u0002J\u000e\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u0006\u001a\u00020\u0007J\u0019\u0010\u001a\u001a\u00020\u001b2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00070\u001c\u00a2\u0006\u0002\u0010\u001dR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/xue/hongshu/utils/CertificateHelper;", "", "()V", "TAG", "", "calculateFingerprint", "certificate", "Ljava/security/cert/X509Certificate;", "algorithm", "calculatePublicKeyPin", "generatePinningConfig", "certificates", "", "getCertificate", "hostname", "port", "", "getCertificateDetails", "Lcom/xue/hongshu/utils/CertificateHelper$CertificateDetails;", "getExtendedKeyUsage", "getPublicKeySize", "publicKey", "Ljava/security/PublicKey;", "getSubjectAlternativeNames", "isSelfSigned", "", "validateCertificateChain", "Lcom/xue/hongshu/utils/CertificateHelper$ValidationResult;", "", "([Ljava/security/cert/X509Certificate;)Lcom/xue/hongshu/utils/CertificateHelper$ValidationResult;", "CertificateCaptureTrustManager", "CertificateDetails", "ValidationResult", "app_release"})
public final class CertificateHelper {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "CertificateHelper";
    @org.jetbrains.annotations.NotNull()
    public static final com.xue.hongshu.utils.CertificateHelper INSTANCE = null;
    
    private CertificateHelper() {
        super();
    }
    
    /**
     * 获取指定域名的SSL证书
     */
    @org.jetbrains.annotations.Nullable()
    public final java.security.cert.X509Certificate getCertificate(@org.jetbrains.annotations.NotNull()
    java.lang.String hostname, int port) {
        return null;
    }
    
    /**
     * 计算证书指纹
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String calculateFingerprint(@org.jetbrains.annotations.NotNull()
    java.security.cert.X509Certificate certificate, @org.jetbrains.annotations.NotNull()
    java.lang.String algorithm) {
        return null;
    }
    
    /**
     * 计算证书的公钥指纹（用于证书固定）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String calculatePublicKeyPin(@org.jetbrains.annotations.NotNull()
    java.security.cert.X509Certificate certificate) {
        return null;
    }
    
    /**
     * 验证证书链
     */
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.utils.CertificateHelper.ValidationResult validateCertificateChain(@org.jetbrains.annotations.NotNull()
    java.security.cert.X509Certificate[] certificates) {
        return null;
    }
    
    /**
     * 获取证书的详细信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.utils.CertificateHelper.CertificateDetails getCertificateDetails(@org.jetbrains.annotations.NotNull()
    java.security.cert.X509Certificate certificate) {
        return null;
    }
    
    /**
     * 获取公钥大小
     */
    private final int getPublicKeySize(java.security.PublicKey publicKey) {
        return 0;
    }
    
    /**
     * 获取主题备用名称
     */
    private final java.util.List<java.lang.String> getSubjectAlternativeNames(java.security.cert.X509Certificate certificate) {
        return null;
    }
    
    /**
     * 获取扩展密钥用法
     */
    private final java.util.List<java.lang.String> getExtendedKeyUsage(java.security.cert.X509Certificate certificate) {
        return null;
    }
    
    /**
     * 检查证书是否为自签名
     */
    public final boolean isSelfSigned(@org.jetbrains.annotations.NotNull()
    java.security.cert.X509Certificate certificate) {
        return false;
    }
    
    /**
     * 生成证书固定配置
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String generatePinningConfig(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends java.security.cert.X509Certificate> certificates) {
        return null;
    }
    
    /**
     * 证书捕获信任管理器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J#\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0016\u00a2\u0006\u0002\u0010\nJ#\u0010\u000b\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0016\u00a2\u0006\u0002\u0010\nJ\u0013\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0016\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"Lcom/xue/hongshu/utils/CertificateHelper$CertificateCaptureTrustManager;", "Ljavax/net/ssl/X509TrustManager;", "()V", "checkClientTrusted", "", "chain", "", "Ljava/security/cert/X509Certificate;", "authType", "", "([Ljava/security/cert/X509Certificate;Ljava/lang/String;)V", "checkServerTrusted", "getAcceptedIssuers", "()[Ljava/security/cert/X509Certificate;", "app_release"})
    static final class CertificateCaptureTrustManager implements javax.net.ssl.X509TrustManager {
        
        public CertificateCaptureTrustManager() {
            super();
        }
        
        @java.lang.Override()
        public void checkClientTrusted(@org.jetbrains.annotations.NotNull()
        java.security.cert.X509Certificate[] chain, @org.jetbrains.annotations.NotNull()
        java.lang.String authType) {
        }
        
        @java.lang.Override()
        public void checkServerTrusted(@org.jetbrains.annotations.NotNull()
        java.security.cert.X509Certificate[] chain, @org.jetbrains.annotations.NotNull()
        java.lang.String authType) {
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
            return null;
        }
    }
    
    /**
     * 证书详细信息数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0002\b*\b\u0087\b\u0018\u00002\u00020\u0001B\u008f\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0003\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u000e\u001a\u00020\u0003\u0012\u0006\u0010\u000f\u001a\u00020\u0003\u0012\u0006\u0010\u0010\u001a\u00020\u0003\u0012\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00030\u0012\u0012\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u0012\u0012\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00030\u0012\u00a2\u0006\u0002\u0010\u0016J\t\u0010*\u001a\u00020\u0003H\u00c6\u0003J\t\u0010+\u001a\u00020\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u0003H\u00c6\u0003J\t\u0010-\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00030\u0012H\u00c6\u0003J\u000f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00140\u0012H\u00c6\u0003J\u000f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00030\u0012H\u00c6\u0003J\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\t\u00102\u001a\u00020\u0003H\u00c6\u0003J\t\u00103\u001a\u00020\u0007H\u00c6\u0003J\t\u00104\u001a\u00020\u0007H\u00c6\u0003J\t\u00105\u001a\u00020\u0003H\u00c6\u0003J\t\u00106\u001a\u00020\u0003H\u00c6\u0003J\t\u00107\u001a\u00020\fH\u00c6\u0003J\t\u00108\u001a\u00020\fH\u00c6\u0003J\u00b1\u0001\u00109\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00032\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00030\u00122\u000e\b\u0002\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u00122\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00030\u0012H\u00c6\u0001J\u0013\u0010:\u001a\u00020\u00142\b\u0010;\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010<\u001a\u00020\fH\u00d6\u0001J\t\u0010=\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00030\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0018R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001aR\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001aR\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001aR\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001aR\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001aR\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001aR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001aR\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00030\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0018R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\'R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001f\u00a8\u0006>"}, d2 = {"Lcom/xue/hongshu/utils/CertificateHelper$CertificateDetails;", "", "subject", "", "issuer", "serialNumber", "validFrom", "Ljava/util/Date;", "validTo", "signatureAlgorithm", "publicKeyAlgorithm", "publicKeySize", "", "version", "sha1Fingerprint", "sha256Fingerprint", "publicKeyPin", "subjectAlternativeNames", "", "keyUsage", "", "extendedKeyUsage", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Date;Ljava/util/Date;Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getExtendedKeyUsage", "()Ljava/util/List;", "getIssuer", "()Ljava/lang/String;", "getKeyUsage", "getPublicKeyAlgorithm", "getPublicKeyPin", "getPublicKeySize", "()I", "getSerialNumber", "getSha1Fingerprint", "getSha256Fingerprint", "getSignatureAlgorithm", "getSubject", "getSubjectAlternativeNames", "getValidFrom", "()Ljava/util/Date;", "getValidTo", "getVersion", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_release"})
    public static final class CertificateDetails {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String subject = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String issuer = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String serialNumber = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.Date validFrom = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.Date validTo = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String signatureAlgorithm = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String publicKeyAlgorithm = null;
        private final int publicKeySize = 0;
        private final int version = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String sha1Fingerprint = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String sha256Fingerprint = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String publicKeyPin = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> subjectAlternativeNames = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.Boolean> keyUsage = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> extendedKeyUsage = null;
        
        public CertificateDetails(@org.jetbrains.annotations.NotNull()
        java.lang.String subject, @org.jetbrains.annotations.NotNull()
        java.lang.String issuer, @org.jetbrains.annotations.NotNull()
        java.lang.String serialNumber, @org.jetbrains.annotations.NotNull()
        java.util.Date validFrom, @org.jetbrains.annotations.NotNull()
        java.util.Date validTo, @org.jetbrains.annotations.NotNull()
        java.lang.String signatureAlgorithm, @org.jetbrains.annotations.NotNull()
        java.lang.String publicKeyAlgorithm, int publicKeySize, int version, @org.jetbrains.annotations.NotNull()
        java.lang.String sha1Fingerprint, @org.jetbrains.annotations.NotNull()
        java.lang.String sha256Fingerprint, @org.jetbrains.annotations.NotNull()
        java.lang.String publicKeyPin, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> subjectAlternativeNames, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.Boolean> keyUsage, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> extendedKeyUsage) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSubject() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getIssuer() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSerialNumber() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Date getValidFrom() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Date getValidTo() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSignatureAlgorithm() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getPublicKeyAlgorithm() {
            return null;
        }
        
        public final int getPublicKeySize() {
            return 0;
        }
        
        public final int getVersion() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSha1Fingerprint() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSha256Fingerprint() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getPublicKeyPin() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getSubjectAlternativeNames() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.Boolean> getKeyUsage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getExtendedKeyUsage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component10() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component11() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component12() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component13() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.Boolean> component14() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component15() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Date component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Date component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component6() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component7() {
            return null;
        }
        
        public final int component8() {
            return 0;
        }
        
        public final int component9() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.CertificateHelper.CertificateDetails copy(@org.jetbrains.annotations.NotNull()
        java.lang.String subject, @org.jetbrains.annotations.NotNull()
        java.lang.String issuer, @org.jetbrains.annotations.NotNull()
        java.lang.String serialNumber, @org.jetbrains.annotations.NotNull()
        java.util.Date validFrom, @org.jetbrains.annotations.NotNull()
        java.util.Date validTo, @org.jetbrains.annotations.NotNull()
        java.lang.String signatureAlgorithm, @org.jetbrains.annotations.NotNull()
        java.lang.String publicKeyAlgorithm, int publicKeySize, int version, @org.jetbrains.annotations.NotNull()
        java.lang.String sha1Fingerprint, @org.jetbrains.annotations.NotNull()
        java.lang.String sha256Fingerprint, @org.jetbrains.annotations.NotNull()
        java.lang.String publicKeyPin, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> subjectAlternativeNames, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.Boolean> keyUsage, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> extendedKeyUsage) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 验证结果数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0002\u0010\bJ\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J3\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00032\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0006H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\tR\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000b\u00a8\u0006\u0016"}, d2 = {"Lcom/xue/hongshu/utils/CertificateHelper$ValidationResult;", "", "isValid", "", "issues", "", "", "warnings", "(ZLjava/util/List;Ljava/util/List;)V", "()Z", "getIssues", "()Ljava/util/List;", "getWarnings", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "app_release"})
    public static final class ValidationResult {
        private final boolean isValid = false;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> issues = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> warnings = null;
        
        public ValidationResult(boolean isValid, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> issues, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> warnings) {
            super();
        }
        
        public final boolean isValid() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getIssues() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getWarnings() {
            return null;
        }
        
        public final boolean component1() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.CertificateHelper.ValidationResult copy(boolean isValid, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> issues, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> warnings) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}