package com.xue.hongshu.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

object Logger {
    
    private const val TAG = "HongshuLogger"
    private const val LOG_FILE_NAME = "hongshu_logs.txt"
    private const val MAX_LOG_SIZE = 5 * 1024 * 1024 // 5MB
    
    private var context: Context? = null
    private var logFile: File? = null
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    fun init(context: Context) {
        this.context = context
        this.logFile = File(context.filesDir, LOG_FILE_NAME)
    }
    
    fun d(tag: String, message: String) {
        Log.d(tag, message)
        writeToFile("DEBUG", tag, message)
    }
    
    fun i(tag: String, message: String) {
        Log.i(tag, message)
        writeToFile("INFO", tag, message)
    }
    
    fun w(tag: String, message: String, throwable: Throwable? = null) {
        Log.w(tag, message, throwable)
        writeToFile("WARN", tag, message, throwable)
    }
    
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        Log.e(tag, message, throwable)
        writeToFile("ERROR", tag, message, throwable)
    }
    
    // 记录用户操作
    fun logUserAction(action: String, details: String = "") {
        val message = "用户操作: $action${if (details.isNotEmpty()) " - $details" else ""}"
        i("UserAction", message)
    }
    
    // 记录网络请求
    fun logNetworkRequest(url: String, method: String, result: String) {
        val message = "网络请求: $method $url -> $result"
        d("Network", message)
    }
    
    // 记录数据抓取
    fun logDataExtraction(employeeId: String, success: Boolean, details: String = "") {
        val result = if (success) "成功" else "失败"
        val message = "数据抓取: 员工ID=$employeeId, 结果=$result${if (details.isNotEmpty()) ", 详情=$details" else ""}"
        if (success) i("DataExtraction", message) else w("DataExtraction", message)
    }
    
    // 记录错误统计
    fun logErrorStats(errorType: ErrorHandler.ErrorType, count: Int) {
        val message = "错误统计: $errorType 发生 $count 次"
        w("ErrorStats", message)
    }
    
    // 记录配置变更
    fun logConfigChange(configName: String, oldValue: String, newValue: String) {
        val message = "配置变更: $configName 从 '$oldValue' 改为 '$newValue'"
        i("Config", message)
    }
    
    private fun writeToFile(level: String, tag: String, message: String, throwable: Throwable? = null) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val file = logFile ?: return@launch
                
                // 检查文件大小，如果太大则清理
                if (file.exists() && file.length() > MAX_LOG_SIZE) {
                    cleanupLogFile(file)
                }
                
                val timestamp = dateFormat.format(Date())
                val logEntry = buildString {
                    append("[$timestamp] [$level] [$tag] $message")
                    throwable?.let { 
                        append("\n")
                        append(it.stackTraceToString())
                    }
                    append("\n")
                }
                
                FileWriter(file, true).use { writer ->
                    writer.write(logEntry)
                    writer.flush()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to write log to file", e)
            }
        }
    }
    
    private fun cleanupLogFile(file: File) {
        try {
            // 保留最后1MB的日志
            val content = file.readText()
            val keepSize = 1024 * 1024 // 1MB
            if (content.length > keepSize) {
                val keepContent = content.takeLast(keepSize)
                file.writeText("=== 日志文件已清理 ===\n$keepContent")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup log file", e)
            // 如果清理失败，直接删除文件
            file.delete()
        }
    }
    
    // 获取日志文件内容
    fun getLogContent(): String {
        return try {
            logFile?.readText() ?: "日志文件不存在"
        } catch (e: Exception) {
            "读取日志文件失败: ${e.message}"
        }
    }
    
    // 清空日志
    fun clearLogs() {
        try {
            logFile?.delete()
            i(TAG, "日志已清空")
        } catch (e: Exception) {
            e(TAG, "清空日志失败", e)
        }
    }
    
    // 导出日志
    fun exportLogs(context: Context, callback: (File?) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val exportFile = File(context.getExternalFilesDir(null), "hongshu_logs_export_${System.currentTimeMillis()}.txt")
                logFile?.copyTo(exportFile, overwrite = true)
                callback(exportFile)
            } catch (e: Exception) {
                e(TAG, "导出日志失败", e)
                callback(null)
            }
        }
    }
    
    // 获取日志统计信息
    fun getLogStats(): LogStats {
        return try {
            val content = logFile?.readText() ?: ""
            val lines = content.lines()
            
            LogStats(
                totalLines = lines.size,
                errorCount = lines.count { it.contains("[ERROR]") },
                warningCount = lines.count { it.contains("[WARN]") },
                infoCount = lines.count { it.contains("[INFO]") },
                debugCount = lines.count { it.contains("[DEBUG]") },
                fileSize = logFile?.length() ?: 0
            )
        } catch (e: Exception) {
            LogStats()
        }
    }
}

data class LogStats(
    val totalLines: Int = 0,
    val errorCount: Int = 0,
    val warningCount: Int = 0,
    val infoCount: Int = 0,
    val debugCount: Int = 0,
    val fileSize: Long = 0
) {
    fun getFormattedSize(): String {
        return when {
            fileSize < 1024 -> "${fileSize}B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024}KB"
            else -> "${fileSize / (1024 * 1024)}MB"
        }
    }
}
