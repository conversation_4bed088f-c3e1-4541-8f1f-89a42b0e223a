package com.xue.hongshu.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xue.hongshu.utils.Logger
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LogViewerScreen(
    onBackPressed: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val listState = rememberLazyListState()
    
    var logContent by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(true) }
    var filterLevel by remember { mutableStateOf("ALL") }
    var showClearDialog by remember { mutableStateOf(false) }
    
    // 加载日志内容
    LaunchedEffect(Unit) {
        scope.launch {
            logContent = Logger.getLogContent()
            isLoading = false
        }
    }
    
    // 过滤日志
    val filteredLogs = remember(logContent, filterLevel) {
        if (filterLevel == "ALL") {
            logContent.lines()
        } else {
            logContent.lines().filter { it.contains("[$filterLevel]") }
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("系统日志") },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 分享日志
                    IconButton(
                        onClick = {
                            Logger.exportLogs(context) { file ->
                                if (file != null) {
                                    // 这里可以实现分享功能
                                    // 实际项目中需要使用Intent分享文件
                                }
                            }
                        }
                    ) {
                        Icon(Icons.Default.Share, contentDescription = "分享")
                    }
                    
                    // 清空日志
                    IconButton(onClick = { showClearDialog = true }) {
                        Icon(Icons.Default.Clear, contentDescription = "清空")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 过滤器
            LogFilterBar(
                selectedFilter = filterLevel,
                onFilterChanged = { filterLevel = it },
                logStats = Logger.getLogStats()
            )
            
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (filteredLogs.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = if (logContent.isEmpty()) "暂无日志" else "没有符合条件的日志",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                LazyColumn(
                    state = listState,
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(8.dp),
                    verticalArrangement = Arrangement.spacedBy(2.dp)
                ) {
                    items(filteredLogs.size) { index ->
                        LogItem(
                            logLine = filteredLogs[index],
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
                
                // 自动滚动到底部按钮
                LaunchedEffect(filteredLogs.size) {
                    if (filteredLogs.isNotEmpty()) {
                        listState.animateScrollToItem(filteredLogs.size - 1)
                    }
                }
            }
        }
    }
    
    // 清空确认对话框
    if (showClearDialog) {
        AlertDialog(
            onDismissRequest = { showClearDialog = false },
            title = { Text("确认清空") },
            text = { Text("确定要清空所有日志吗？此操作不可撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        Logger.clearLogs()
                        logContent = ""
                        showClearDialog = false
                    }
                ) {
                    Text("清空")
                }
            },
            dismissButton = {
                TextButton(onClick = { showClearDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun LogFilterBar(
    selectedFilter: String,
    onFilterChanged: (String) -> Unit,
    logStats: com.xue.hongshu.utils.LogStats
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "日志过滤",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                val filters = listOf(
                    "ALL" to "全部(${logStats.totalLines})",
                    "ERROR" to "错误(${logStats.errorCount})",
                    "WARN" to "警告(${logStats.warningCount})",
                    "INFO" to "信息(${logStats.infoCount})",
                    "DEBUG" to "调试(${logStats.debugCount})"
                )
                
                filters.forEach { (level, label) ->
                    FilterChip(
                        onClick = { onFilterChanged(level) },
                        label = { Text(label, fontSize = 12.sp) },
                        selected = selectedFilter == level,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun LogItem(
    logLine: String,
    modifier: Modifier = Modifier
) {
    if (logLine.isBlank()) return
    
    val (backgroundColor, textColor) = when {
        logLine.contains("[ERROR]") -> MaterialTheme.colorScheme.errorContainer to MaterialTheme.colorScheme.onErrorContainer
        logLine.contains("[WARN]") -> Color(0xFFFFF3E0) to Color(0xFFE65100)
        logLine.contains("[INFO]") -> MaterialTheme.colorScheme.primaryContainer to MaterialTheme.colorScheme.onPrimaryContainer
        logLine.contains("[DEBUG]") -> MaterialTheme.colorScheme.surfaceVariant to MaterialTheme.colorScheme.onSurfaceVariant
        else -> MaterialTheme.colorScheme.surface to MaterialTheme.colorScheme.onSurface
    }
    
    SelectionContainer {
        Text(
            text = logLine,
            modifier = modifier
                .background(backgroundColor)
                .padding(horizontal = 8.dp, vertical = 4.dp),
            style = MaterialTheme.typography.bodySmall.copy(
                fontFamily = FontFamily.Monospace,
                fontSize = 11.sp
            ),
            color = textColor
        )
    }
}
