package com.xue.hongshu.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.xue.hongshu.ui.component.AddEmployeeDialog
import com.xue.hongshu.ui.component.EmployeeCard
import com.xue.hongshu.ui.component.StatisticsCard
import com.xue.hongshu.ui.component.SystemMonitorCard
import com.xue.hongshu.utils.ConfigManager
import com.xue.hongshu.utils.ErrorHandler
import com.xue.hongshu.utils.Logger
import com.xue.hongshu.utils.NetworkMonitor
import com.xue.hongshu.utils.NetworkState
import com.xue.hongshu.utils.ConnectionQuality
import com.xue.hongshu.utils.WebViewTester
import com.xue.hongshu.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    onNavigateToWebView: (String) -> Unit,
    onNavigateToLogs: () -> Unit = {}
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val employees by viewModel.employees.collectAsStateWithLifecycle()

    val context = androidx.compose.ui.platform.LocalContext.current
    val configManager = remember { ConfigManager(context) }
    val networkMonitor = remember { NetworkMonitor(context) }

    var showAddDialog by remember { mutableStateOf(false) }
    var showSystemMonitor by remember { mutableStateOf(false) }
    var showDebugDialog by remember { mutableStateOf(false) }
    var debugMessage by remember { mutableStateOf("") }

    // 网络状态监控
    val networkState by networkMonitor.networkState.collectAsStateWithLifecycle()
    val connectionQuality by networkMonitor.connectionQuality.collectAsStateWithLifecycle()

    // 日志和错误统计
    var logStats by remember { mutableStateOf(Logger.getLogStats()) }
    var errorStats by remember { mutableStateOf(ErrorHandler.getErrorStats()) }

    // 定期更新统计信息
    LaunchedEffect(Unit) {
        networkMonitor.startMonitoring()
        while (true) {
            kotlinx.coroutines.delay(5000) // 每5秒更新一次
            logStats = Logger.getLogStats()
            errorStats = ErrorHandler.getErrorStats()
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            networkMonitor.stopMonitoring()
        }
    }
    
    // 显示消息和错误
    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // 这里可以显示Snackbar
            viewModel.clearMessage()
        }
    }
    
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // 这里可以显示错误Snackbar
            viewModel.clearError()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("小红书监控") },
                actions = {
                    // 调试按钮（仅在Debug模式下显示）
                    // if (BuildConfig.DEBUG) {
                        IconButton(onClick = { showDebugDialog = true }) {
                            Icon(Icons.Default.Settings, contentDescription = "调试")
                        }
                    // }

                    IconButton(onClick = { showSystemMonitor = !showSystemMonitor }) {
                        Icon(Icons.Default.Info, contentDescription = "系统监控")
                    }

                    IconButton(
                        onClick = { viewModel.startBatchCheck() },
                        enabled = !uiState.isBatchChecking && employees.isNotEmpty()
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = "批量检查")
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { showAddDialog = true }
            ) {
                Icon(Icons.Default.Add, contentDescription = "添加员工")
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // 统计卡片
            StatisticsCard(
                statistics = uiState.statistics,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(16.dp))

            // 系统监控卡片
            if (showSystemMonitor) {
                SystemMonitorCard(
                    configManager = configManager,
                    networkState = networkState,
                    connectionQuality = connectionQuality,
                    logStats = logStats,
                    errorStats = errorStats,
                    modifier = Modifier.fillMaxWidth(),
                    onViewLogs = onNavigateToLogs,
                    onClearLogs = {
                        Logger.clearLogs()
                        logStats = Logger.getLogStats()
                    },
                    onResetConfig = {
                        configManager.resetConfig()
                        ErrorHandler.clearErrorStats()
                        errorStats = ErrorHandler.getErrorStats()
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))
            }

            // 批量检查进度
            if (uiState.isBatchChecking) {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "批量检查进行中...",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        LinearProgressIndicator(
                            progress = uiState.batchCheckProgress,
                            modifier = Modifier.fillMaxWidth()
                        )
                        Text(
                            text = "${(uiState.batchCheckProgress * 100).toInt()}%",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 员工列表标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "员工列表 (${employees.size})",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 员工列表
            if (employees.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "暂无员工数据",
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "点击右下角按钮添加员工",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(employees) { employeeWithStats ->
                        EmployeeCard(
                            employeeWithStats = employeeWithStats,
                            isChecking = uiState.currentCheckingEmployeeId == employeeWithStats.employee.id,
                            onCheckClick = { 
                                onNavigateToWebView(employeeWithStats.employee.id)
                            },
                            onDeleteClick = { 
                                viewModel.deleteEmployee(employeeWithStats.employee)
                            }
                        )
                    }
                }
            }
        }
    }
    
    // 添加员工对话框
    if (showAddDialog) {
        AddEmployeeDialog(
            onDismiss = { showAddDialog = false },
            onConfirm = { name, xhsUserId, xhsUserName ->
                viewModel.addEmployee(name, xhsUserId, xhsUserName)
                showAddDialog = false
            }
        )
    }

    // 调试对话框
    if (showDebugDialog) {
        AlertDialog(
            onDismissRequest = { showDebugDialog = false },
            title = { Text("WebView调试") },
            text = {
                Column {
                    Text("选择调试功能:")
                    Spacer(modifier = Modifier.height(8.dp))

                    Button(
                        onClick = {
                            WebViewTester.testBasicWebView(context) { success, message ->
                                debugMessage = "基础测试: $message"
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("测试WebView基础功能")
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Button(
                        onClick = {
                            WebViewTester.testJavaScriptExecution(context) { success, message ->
                                debugMessage = "JS测试: $message"
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("测试JavaScript执行")
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    if (employees.isNotEmpty()) {
                        Button(
                            onClick = {
                                val firstEmployee = employees.first().employee
                                WebViewTester.testXhsAccess(context, firstEmployee.xhsUserId) { success, message ->
                                    debugMessage = "小红书测试: $message"
                                }
                            },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("测试小红书访问")
                        }
                    }

                    if (debugMessage.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = debugMessage,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showDebugDialog = false }) {
                    Text("关闭")
                }
            }
        )
    }
}
