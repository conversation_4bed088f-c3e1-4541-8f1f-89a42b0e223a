package com.xue.hongshu.ui.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.xue.hongshu.utils.ConfigManager
import com.xue.hongshu.utils.ErrorHandler
import com.xue.hongshu.utils.LogStats
import com.xue.hongshu.utils.NetworkState
import com.xue.hongshu.utils.ConnectionQuality

@Composable
fun SystemMonitorCard(
    configManager: ConfigManager,
    networkState: NetworkState,
    connectionQuality: ConnectionQuality,
    logStats: LogStats,
    errorStats: Map<ErrorHandler.ErrorType, Int>,
    modifier: Modifier = Modifier,
    onViewLogs: () -> Unit = {},
    onClearLogs: () -> Unit = {},
    onResetConfig: () -> Unit = {}
) {
    var expanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "系统监控",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                TextButton(onClick = { expanded = !expanded }) {
                    Text(if (expanded) "收起" else "展开")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 基础状态信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatusItem(
                    label = "网络",
                    value = getNetworkDisplayText(networkState),
                    color = getNetworkColor(connectionQuality)
                )
                
                StatusItem(
                    label = "检测级别",
                    value = getDetectionLevelText(configManager.getDetectionLevel()),
                    color = getDetectionLevelColor(configManager.getDetectionLevel())
                )
                
                StatusItem(
                    label = "错误",
                    value = "${logStats.errorCount}",
                    color = if (logStats.errorCount > 0) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.tertiary
                )
            }
            
            if (expanded) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // 详细信息
                LazyColumn(
                    modifier = Modifier.heightIn(max = 300.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 网络详情
                    item {
                        DetailSection(
                            title = "网络状态",
                            items = listOf(
                                "类型" to getNetworkDisplayText(networkState),
                                "质量" to getConnectionQualityText(connectionQuality),
                                "建议超时" to "30秒" // 固定值，避免创建NetworkMonitor实例
                            )
                        )
                    }
                    
                    // 配置详情
                    item {
                        DetailSection(
                            title = "当前配置",
                            items = listOf(
                                "检测级别" to getDetectionLevelText(configManager.getDetectionLevel()),
                                "策略模式" to getStrategyModeText(configManager.getStrategyMode()),
                                "重试次数" to "${configManager.getAdaptiveRetryCount()}",
                                "基础延迟" to "${configManager.getBaseDelay()}ms",
                                "连续失败" to "${configManager.getConsecutiveFailures()}次"
                            )
                        )
                    }
                    
                    // 日志统计
                    item {
                        DetailSection(
                            title = "日志统计",
                            items = listOf(
                                "总行数" to "${logStats.totalLines}",
                                "错误" to "${logStats.errorCount}",
                                "警告" to "${logStats.warningCount}",
                                "信息" to "${logStats.infoCount}",
                                "文件大小" to logStats.getFormattedSize()
                            )
                        )
                    }
                    
                    // 错误统计
                    if (errorStats.isNotEmpty()) {
                        item {
                            DetailSection(
                                title = "错误统计",
                                items = errorStats.map { (type, count) ->
                                    getErrorTypeText(type) to "${count}次"
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onViewLogs,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.Info, contentDescription = null, modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("查看日志")
                    }
                    
                    OutlinedButton(
                        onClick = onClearLogs,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("清空日志")
                    }
                    
                    OutlinedButton(
                        onClick = onResetConfig,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.Warning, contentDescription = null, modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("重置配置")
                    }
                }
            }
        }
    }
}

@Composable
private fun StatusItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun DetailSection(
    title: String,
    items: List<Pair<String, String>>
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            items.forEach { (key, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = key,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = value,
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

// 辅助函数
private fun getNetworkDisplayText(state: NetworkState): String {
    return when (state) {
        NetworkState.WIFI -> "WiFi"
        NetworkState.CELLULAR -> "移动网络"
        NetworkState.ETHERNET -> "以太网"
        NetworkState.OTHER -> "其他"
        NetworkState.DISCONNECTED -> "未连接"
        NetworkState.UNKNOWN -> "未知"
    }
}

private fun getConnectionQualityText(quality: ConnectionQuality): String {
    return when (quality) {
        ConnectionQuality.EXCELLENT -> "优秀"
        ConnectionQuality.GOOD -> "良好"
        ConnectionQuality.FAIR -> "一般"
        ConnectionQuality.POOR -> "较差"
        ConnectionQuality.NONE -> "无连接"
        ConnectionQuality.UNKNOWN -> "未知"
    }
}

@Composable
private fun getNetworkColor(quality: ConnectionQuality): Color {
    return when (quality) {
        ConnectionQuality.EXCELLENT -> Color(0xFF4CAF50)
        ConnectionQuality.GOOD -> Color(0xFF8BC34A)
        ConnectionQuality.FAIR -> Color(0xFFFF9800)
        ConnectionQuality.POOR -> Color(0xFFFF5722)
        ConnectionQuality.NONE -> MaterialTheme.colorScheme.error
        ConnectionQuality.UNKNOWN -> MaterialTheme.colorScheme.onSurfaceVariant
    }
}

private fun getDetectionLevelText(level: ConfigManager.DetectionLevel): String {
    return when (level) {
        ConfigManager.DetectionLevel.LOW -> "低"
        ConfigManager.DetectionLevel.MEDIUM -> "中"
        ConfigManager.DetectionLevel.HIGH -> "高"
    }
}

@Composable
private fun getDetectionLevelColor(level: ConfigManager.DetectionLevel): Color {
    return when (level) {
        ConfigManager.DetectionLevel.LOW -> Color(0xFF4CAF50)
        ConfigManager.DetectionLevel.MEDIUM -> Color(0xFFFF9800)
        ConfigManager.DetectionLevel.HIGH -> Color(0xFFFF5722)
    }
}

private fun getStrategyModeText(mode: ConfigManager.StrategyMode): String {
    return when (mode) {
        ConfigManager.StrategyMode.NORMAL -> "正常"
        ConfigManager.StrategyMode.STEALTH -> "隐蔽"
        ConfigManager.StrategyMode.AGGRESSIVE -> "激进"
    }
}

private fun getErrorTypeText(type: ErrorHandler.ErrorType): String {
    return when (type) {
        ErrorHandler.ErrorType.NETWORK_ERROR -> "网络错误"
        ErrorHandler.ErrorType.PARSING_ERROR -> "解析错误"
        ErrorHandler.ErrorType.PAGE_LOAD_ERROR -> "页面错误"
        ErrorHandler.ErrorType.DETECTION_ERROR -> "检测错误"
        ErrorHandler.ErrorType.TIMEOUT_ERROR -> "超时错误"
        ErrorHandler.ErrorType.SSL_ERROR -> "SSL错误"
        ErrorHandler.ErrorType.CERTIFICATE_ERROR -> "证书错误"
        ErrorHandler.ErrorType.UNKNOWN_ERROR -> "未知错误"
    }
}
