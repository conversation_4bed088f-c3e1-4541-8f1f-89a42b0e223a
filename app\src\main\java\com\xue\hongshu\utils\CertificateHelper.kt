package com.xue.hongshu.utils

import android.util.Log
import java.io.ByteArrayInputStream
import java.net.URL
import java.security.MessageDigest
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.util.*
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

/**
 * SSL证书辅助工具
 * 用于获取和分析SSL证书信息
 */
object CertificateHelper {
    
    private const val TAG = "CertificateHelper"
    
    /**
     * 获取指定域名的SSL证书
     */
    fun getCertificate(hostname: String, port: Int = 443): X509Certificate? {
        return try {
            Log.d(TAG, "获取证书: $hostname:$port")
            
            val url = URL("https://$hostname:$port")
            val connection = url.openConnection() as HttpsURLConnection
            
            // 设置自定义SSL上下文以获取证书
            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(null, arrayOf(CertificateCaptureTrustManager()), null)
            connection.sslSocketFactory = sslContext.socketFactory
            
            connection.connectTimeout = 10000
            connection.readTimeout = 10000
            
            connection.connect()
            
            val certificates = connection.serverCertificates
            if (certificates.isNotEmpty()) {
                certificates[0] as X509Certificate
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取证书失败: $hostname", e)
            null
        }
    }
    
    /**
     * 计算证书指纹
     */
    fun calculateFingerprint(certificate: X509Certificate, algorithm: String = "SHA-256"): String? {
        return try {
            val digest = MessageDigest.getInstance(algorithm)
            val fingerprint = digest.digest(certificate.encoded)
            fingerprint.joinToString(":") { "%02X".format(it) }
        } catch (e: Exception) {
            Log.e(TAG, "计算证书指纹失败", e)
            null
        }
    }
    
    /**
     * 计算证书的公钥指纹（用于证书固定）
     */
    fun calculatePublicKeyPin(certificate: X509Certificate): String? {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val publicKeyBytes = certificate.publicKey.encoded
            val pin = digest.digest(publicKeyBytes)
            Base64.getEncoder().encodeToString(pin)
        } catch (e: Exception) {
            Log.e(TAG, "计算公钥指纹失败", e)
            null
        }
    }
    
    /**
     * 验证证书链
     */
    fun validateCertificateChain(certificates: Array<X509Certificate>): ValidationResult {
        val issues = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        try {
            if (certificates.isEmpty()) {
                issues.add("证书链为空")
                return ValidationResult(false, issues, warnings)
            }
            
            val serverCert = certificates[0]
            val now = Date()
            
            // 检查证书有效期
            try {
                serverCert.checkValidity(now)
            } catch (e: Exception) {
                issues.add("证书有效期检查失败: ${e.message}")
            }
            
            // 检查证书即将过期
            val daysUntilExpiry = (serverCert.notAfter.time - now.time) / (1000 * 60 * 60 * 24)
            if (daysUntilExpiry < 30) {
                warnings.add("证书将在${daysUntilExpiry}天后过期")
            }
            
            // 检查签名算法
            val sigAlg = serverCert.sigAlgName.lowercase()
            if (sigAlg.contains("sha1")) {
                warnings.add("证书使用SHA1签名算法，安全性较低")
            }
            
            // 检查密钥长度
            val publicKey = serverCert.publicKey
            if (publicKey.algorithm == "RSA") {
                val keySize = (publicKey as java.security.interfaces.RSAPublicKey).modulus.bitLength()
                if (keySize < 2048) {
                    warnings.add("RSA密钥长度($keySize)小于推荐的2048位")
                }
            }
            
            // 检查证书链完整性
            if (certificates.size == 1) {
                warnings.add("证书链只包含服务器证书，可能缺少中间证书")
            }
            
            // 验证证书链
            for (i in 0 until certificates.size - 1) {
                try {
                    certificates[i].verify(certificates[i + 1].publicKey)
                } catch (e: Exception) {
                    issues.add("证书链验证失败: 证书${i}无法被证书${i+1}验证")
                }
            }
            
        } catch (e: Exception) {
            issues.add("证书链验证异常: ${e.message}")
        }
        
        return ValidationResult(issues.isEmpty(), issues, warnings)
    }
    
    /**
     * 获取证书的详细信息
     */
    fun getCertificateDetails(certificate: X509Certificate): CertificateDetails {
        return CertificateDetails(
            subject = certificate.subjectDN.name,
            issuer = certificate.issuerDN.name,
            serialNumber = certificate.serialNumber.toString(16),
            validFrom = certificate.notBefore,
            validTo = certificate.notAfter,
            signatureAlgorithm = certificate.sigAlgName,
            publicKeyAlgorithm = certificate.publicKey.algorithm,
            publicKeySize = getPublicKeySize(certificate.publicKey),
            version = certificate.version,
            sha1Fingerprint = calculateFingerprint(certificate, "SHA-1") ?: "",
            sha256Fingerprint = calculateFingerprint(certificate, "SHA-256") ?: "",
            publicKeyPin = calculatePublicKeyPin(certificate) ?: "",
            subjectAlternativeNames = getSubjectAlternativeNames(certificate),
            keyUsage = certificate.keyUsage?.toList() ?: emptyList(),
            extendedKeyUsage = getExtendedKeyUsage(certificate)
        )
    }
    
    /**
     * 获取公钥大小
     */
    private fun getPublicKeySize(publicKey: java.security.PublicKey): Int {
        return when (publicKey.algorithm) {
            "RSA" -> (publicKey as java.security.interfaces.RSAPublicKey).modulus.bitLength()
            "EC" -> (publicKey as java.security.interfaces.ECPublicKey).params.order.bitLength()
            else -> 0
        }
    }
    
    /**
     * 获取主题备用名称
     */
    private fun getSubjectAlternativeNames(certificate: X509Certificate): List<String> {
        return try {
            certificate.subjectAlternativeNames?.mapNotNull { san ->
                when (san[0] as Int) {
                    2 -> san[1] as String // DNS名称
                    7 -> san[1] as String // IP地址
                    else -> null
                }
            } ?: emptyList()
        } catch (e: Exception) {
            Log.w(TAG, "获取SAN失败", e)
            emptyList()
        }
    }
    
    /**
     * 获取扩展密钥用法
     */
    private fun getExtendedKeyUsage(certificate: X509Certificate): List<String> {
        return try {
            certificate.extendedKeyUsage ?: emptyList()
        } catch (e: Exception) {
            Log.w(TAG, "获取扩展密钥用法失败", e)
            emptyList()
        }
    }
    
    /**
     * 检查证书是否为自签名
     */
    fun isSelfSigned(certificate: X509Certificate): Boolean {
        return try {
            certificate.verify(certificate.publicKey)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 生成证书固定配置
     */
    fun generatePinningConfig(certificates: List<X509Certificate>): String {
        val pins = certificates.mapNotNull { cert ->
            calculatePublicKeyPin(cert)?.let { pin ->
                "            <pin digest=\"SHA-256\">$pin</pin>"
            }
        }
        
        return if (pins.isNotEmpty()) {
            """
            <pin-set expiration="${Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)}">
${pins.joinToString("\n")}
            </pin-set>
            """.trimIndent()
        } else {
            "<!-- 无法生成证书固定配置 -->"
        }
    }
    
    /**
     * 证书捕获信任管理器
     */
    private class CertificateCaptureTrustManager : X509TrustManager {
        override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
        
        override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
            // 不进行验证，只是为了获取证书
        }
        
        override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
    }
    
    /**
     * 验证结果数据类
     */
    data class ValidationResult(
        val isValid: Boolean,
        val issues: List<String>,
        val warnings: List<String>
    )
    
    /**
     * 证书详细信息数据类
     */
    data class CertificateDetails(
        val subject: String,
        val issuer: String,
        val serialNumber: String,
        val validFrom: Date,
        val validTo: Date,
        val signatureAlgorithm: String,
        val publicKeyAlgorithm: String,
        val publicKeySize: Int,
        val version: Int,
        val sha1Fingerprint: String,
        val sha256Fingerprint: String,
        val publicKeyPin: String,
        val subjectAlternativeNames: List<String>,
        val keyUsage: List<Boolean>,
        val extendedKeyUsage: List<String>
    )
}
