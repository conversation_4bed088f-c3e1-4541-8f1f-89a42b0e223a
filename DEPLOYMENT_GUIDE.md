# 部署指导文档

## 🚀 快速开始

### 1. 环境准备
```bash
# 检查Java版本 (需要JDK 11+)
java -version

# 检查Android SDK
echo $ANDROID_HOME
```

### 2. 项目配置
```kotlin
// 在 local.properties 中配置SDK路径
sdk.dir=/path/to/android/sdk

// 可选：配置签名信息
KEYSTORE_FILE=path/to/keystore
KEYSTORE_PASSWORD=your_password
KEY_ALIAS=your_alias
KEY_PASSWORD=your_key_password
```

### 3. 依赖同步
```bash
# 清理项目
./gradlew clean

# 同步依赖
./gradlew build --refresh-dependencies
```

## 📦 构建配置

### Debug构建
```bash
# 构建Debug APK
./gradlew assembleDebug

# 安装到设备
./gradlew installDebug
```

### Release构建
```bash
# 构建Release APK
./gradlew assembleRelease

# 构建AAB (Google Play)
./gradlew bundleRelease
```

## 🔧 自定义配置

### 1. 修改应用信息
```kotlin
// app/build.gradle.kts
android {
    defaultConfig {
        applicationId = "com.yourcompany.hongshu"
        versionCode = 1
        versionName = "1.0.0"
    }
}
```

### 2. 配置网络安全
```xml
<!-- app/src/main/res/xml/network_security_config.xml -->
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">xiaohongshu.com</domain>
    </domain-config>
</network-security-config>
```

### 3. 自定义反检测策略
```kotlin
// 在 AntiDetectionHelper.kt 中修改
private val userAgents = listOf(
    "你的自定义User-Agent",
    // 添加更多...
)

fun getCustomDelay(): Long {
    // 自定义延迟策略
    return Random.nextLong(3000, 10000)
}
```

## 🛡️ 安全配置

### 1. 代码混淆
```kotlin
// app/build.gradle.kts
android {
    buildTypes {
        release {
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
}
```

### 2. ProGuard规则
```proguard
# app/proguard-rules.pro

# 保留实体类
-keep class com.xue.hongshu.data.entity.** { *; }

# 保留WebView JavaScript接口
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 保留Room数据库
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *
```

## 📊 性能优化

### 1. 启用R8优化
```kotlin
android {
    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
        }
    }
}
```

### 2. 多APK配置
```kotlin
android {
    splits {
        abi {
            isEnable = true
            reset()
            include("arm64-v8a", "armeabi-v7a")
            isUniversalApk = false
        }
    }
}
```

## 🔍 调试配置

### 1. 启用网络日志
```kotlin
// 在 Application 类中
if (BuildConfig.DEBUG) {
    Logger.setLogLevel(Logger.DEBUG)
}
```

### 2. WebView调试
```kotlin
// 在 WebViewActivity 中
if (BuildConfig.DEBUG) {
    WebView.setWebContentsDebuggingEnabled(true)
}
```

## 📱 测试部署

### 1. 单元测试
```bash
# 运行单元测试
./gradlew test

# 生成测试报告
./gradlew testDebugUnitTest --continue
```

### 2. UI测试
```bash
# 运行UI测试
./gradlew connectedAndroidTest
```

### 3. 性能测试
```bash
# 使用Benchmark库进行性能测试
./gradlew connectedBenchmarkAndroidTest
```

## 🚀 生产部署

### 1. 签名配置
```kotlin
// app/build.gradle.kts
android {
    signingConfigs {
        create("release") {
            storeFile = file("../keystore/release.keystore")
            storePassword = "your_store_password"
            keyAlias = "your_key_alias"
            keyPassword = "your_key_password"
        }
    }
    
    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
        }
    }
}
```

### 2. 版本管理
```kotlin
// 自动版本号管理
def getVersionCode() {
    return Integer.parseInt(new Date().format("yyyyMMdd"))
}

android {
    defaultConfig {
        versionCode = getVersionCode()
        versionName = "1.0.${getVersionCode()}"
    }
}
```

## 📋 部署检查清单

### 发布前检查
- [ ] 所有功能测试通过
- [ ] 性能测试达标
- [ ] 内存泄漏检查
- [ ] 网络安全配置
- [ ] 代码混淆启用
- [ ] 签名配置正确
- [ ] 版本信息更新
- [ ] 权限声明完整

### 发布后监控
- [ ] 崩溃率监控
- [ ] 性能指标监控
- [ ] 用户反馈收集
- [ ] 日志分析
- [ ] 更新策略制定

## 🔧 故障排除

### 常见构建问题
1. **Gradle同步失败**
   ```bash
   # 清理Gradle缓存
   ./gradlew clean
   rm -rf ~/.gradle/caches/
   ```

2. **依赖冲突**
   ```bash
   # 查看依赖树
   ./gradlew app:dependencies
   ```

3. **内存不足**
   ```kotlin
   // gradle.properties
   org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m
   ```

### 运行时问题
1. **WebView崩溃**
   - 检查WebView版本兼容性
   - 添加异常捕获机制

2. **网络请求失败**
   - 检查网络权限
   - 验证证书配置

3. **数据库错误**
   - 检查数据库版本迁移
   - 验证实体类定义

## 📞 技术支持

遇到部署问题时：
1. 查看构建日志
2. 检查环境配置
3. 参考官方文档
4. 联系开发团队

---

**注意**：生产环境部署前请务必进行充分测试，确保应用稳定性和安全性。
