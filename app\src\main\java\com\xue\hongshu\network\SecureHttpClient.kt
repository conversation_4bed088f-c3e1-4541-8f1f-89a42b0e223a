package com.xue.hongshu.network

import android.content.Context
import android.util.Log
import okhttp3.*
import okhttp3.logging.HttpLoggingInterceptor
import java.io.IOException
import java.security.cert.CertificateException
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.*

/**
 * 安全的HTTP客户端配置
 * 提供SSL/TLS安全配置和证书验证
 */
object SecureHttpClient {
    
    private const val TAG = "SecureHttpClient"
    
    /**
     * 创建安全的OkHttpClient实例
     */
    fun createSecureClient(context: Context, enableDebugLogging: Boolean = false): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
        
        // 添加日志拦截器（仅调试模式）
        if (enableDebugLogging) {
            val loggingInterceptor = HttpLoggingInterceptor { message ->
                Log.d(TAG, message)
            }.apply {
                level = HttpLoggingInterceptor.Level.HEADERS
            }
            builder.addInterceptor(loggingInterceptor)
        }
        
        // 添加SSL错误处理拦截器
        builder.addInterceptor(SslErrorInterceptor())
        
        // 配置SSL/TLS
        configureSsl(builder, context)
        
        return builder.build()
    }
    
    /**
     * 配置SSL/TLS设置
     */
    private fun configureSsl(builder: OkHttpClient.Builder, context: Context) {
        try {
            // 创建信任管理器
            val trustManager = createTrustManager()
            
            // 创建SSL上下文
            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(null, arrayOf(trustManager), null)
            
            // 配置SSL Socket Factory
            val sslSocketFactory = sslContext.socketFactory
            builder.sslSocketFactory(sslSocketFactory, trustManager)
            
            // 配置主机名验证器
            builder.hostnameVerifier(createHostnameVerifier())
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to configure SSL", e)
        }
    }
    
    /**
     * 创建自定义信任管理器
     */
    private fun createTrustManager(): X509TrustManager {
        return object : X509TrustManager {
            override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
                // 客户端证书验证（通常不需要）
            }
            
            override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
                try {
                    // 验证证书链
                    if (chain.isEmpty()) {
                        throw CertificateException("Certificate chain is empty")
                    }
                    
                    val serverCert = chain[0]
                    
                    // 检查证书有效期
                    serverCert.checkValidity()
                    
                    // 检查证书颁发者
                    val issuer = serverCert.issuerDN.name
                    Log.d(TAG, "Certificate issuer: $issuer")
                    
                    // 对于小红书域名，进行额外验证
                    val subject = serverCert.subjectDN.name
                    if (subject.contains("xiaohongshu.com") || subject.contains("xhscdn.com")) {
                        Log.d(TAG, "Validating XHS certificate: $subject")
                        // 这里可以添加更严格的证书验证逻辑
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Server certificate validation failed", e)
                    throw CertificateException("Server certificate validation failed", e)
                }
            }
            
            override fun getAcceptedIssuers(): Array<X509Certificate> {
                return arrayOf()
            }
        }
    }
    
    /**
     * 创建主机名验证器
     */
    private fun createHostnameVerifier(): HostnameVerifier {
        return HostnameVerifier { hostname, session ->
            try {
                // 验证主机名
                val peerCertificates = session.peerCertificates
                if (peerCertificates.isNotEmpty()) {
                    val cert = peerCertificates[0] as X509Certificate
                    val subject = cert.subjectDN.name
                    
                    Log.d(TAG, "Verifying hostname: $hostname against certificate: $subject")
                    
                    // 允许的主机名列表
                    val allowedHosts = listOf(
                        "xiaohongshu.com",
                        "www.xiaohongshu.com",
                        "fe-video-qc.xhscdn.com",
                        "sns-avatar-qc.xhscdn.com",
                        "ci.xiaohongshu.com",
                        "edith.xiaohongshu.com",
                        "picasso.xiaohongshu.com"
                    )
                    
                    // 检查主机名是否在允许列表中
                    val isAllowed = allowedHosts.any { allowedHost ->
                        hostname.equals(allowedHost, ignoreCase = true) ||
                        hostname.endsWith(".$allowedHost", ignoreCase = true)
                    }
                    
                    if (!isAllowed) {
                        Log.w(TAG, "Hostname $hostname not in allowed list")
                    }
                    
                    return@HostnameVerifier isAllowed
                }
                
                Log.w(TAG, "No peer certificates found for hostname: $hostname")
                return@HostnameVerifier false
                
            } catch (e: Exception) {
                Log.e(TAG, "Hostname verification failed for: $hostname", e)
                return@HostnameVerifier false
            }
        }
    }
    
    /**
     * SSL错误处理拦截器
     */
    private class SslErrorInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            
            return try {
                chain.proceed(request)
            } catch (e: SSLException) {
                Log.e(TAG, "SSL error for ${request.url}", e)
                
                // 分析SSL错误类型
                val errorType = when {
                    e.message?.contains("certificate", ignoreCase = true) == true -> "证书错误"
                    e.message?.contains("handshake", ignoreCase = true) == true -> "握手失败"
                    e.message?.contains("protocol", ignoreCase = true) == true -> "协议错误"
                    else -> "SSL连接错误"
                }
                
                Log.e(TAG, "SSL error type: $errorType")
                throw IOException("SSL连接失败: $errorType", e)
            }
        }
    }
}
