2025-08-08 00:14:07.173  4316-4316  UserAction              com.xue.hongshu                      I  用户操作: 启动WebView检查 - 员工ID: fb69f122-84a9-43ba-93f6-6de78f6c2fb8
--------- beginning of system
2025-08-08 00:14:07.180  4316-4316  UserAction              com.xue.hongshu                      I  用户操作: 启动WebView - 员工: 薛博文, 小红书ID: 64d1afd0000000000b007f59
2025-08-08 00:14:07.197  4316-4316  ActivityThread          com.xue.hongshu                      W  handleWindowVisibility: no activity for token android.os.BinderProxy@4827c3b
2025-08-08 00:14:07.246  4316-4316  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/view/RenderNode;->getScaleX()F (dark greylist, linking)
2025-08-08 00:14:07.269  4316-4316  WebViewFactory          com.xue.hongshu                      I  Loading com.android.webview version 91.0.4472.114 (code 447211456)
2025-08-08 00:14:07.274  4316-4316  com.xue.hongsh          com.xue.hongshu                      I  The ClassLoaderContext is a special shared library.
2025-08-08 00:14:07.379  4316-4316  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/os/Trace;->traceBegin(JLjava/lang/String;)V (light greylist, reflection)
2025-08-08 00:14:07.379  4316-4316  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/os/Trace;->traceEnd(J)V (light greylist, reflection)
2025-08-08 00:14:07.450  4316-4316  cr_LibraryLoader        com.xue.hongshu                      I  Loaded native library version number "91.0.4472.114"
2025-08-08 00:14:07.450  4316-4316  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden field Ljava/util/Collections$SynchronizedCollection;->mutex:Ljava/lang/Object; (dark greylist, reflection)
2025-08-08 00:14:07.450  4316-4316  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden field Ljava/util/Collections$SynchronizedCollection;->c:Ljava/util/Collection; (light greylist, reflection)
2025-08-08 00:14:07.450  4316-4316  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Ljava/util/Collections$SynchronizedSet;-><init>(Ljava/util/Set;Ljava/lang/Object;)V (dark greylist, reflection)
2025-08-08 00:14:07.450  4316-4316  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Ljava/util/Collections$SynchronizedCollection;-><init>(Ljava/util/Collection;Ljava/lang/Object;)V (dark greylist, reflection)
2025-08-08 00:14:07.453  4316-4316  cr_CachingUmaRecorder   com.xue.hongshu                      I  Flushed 6 samples from 6 histograms.
2025-08-08 00:14:07.499  4316-4381  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/content/Context;->bindServiceAsUser(Landroid/content/Intent;Landroid/content/ServiceConnection;ILandroid/os/Handler;Landroid/os/UserHandle;)Z (light greylist, reflection)
2025-08-08 00:14:07.885  4316-4404  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/media/AudioManager;->getOutputLatency(I)I (light greylist, reflection)
2025-08-08 00:14:07.901  4316-4414  <no-tag>                com.xue.hongshu                      I  fastpipe: Connect success
2025-08-08 00:14:07.901  4316-4414  HostConnection          com.xue.hongshu                      D  HostRPC::connect sucess: app=com.xue.hongshu, pid=4316, tid=4414, this=0x763878d5a580
2025-08-08 00:14:07.902  4316-4414  HostConnection          com.xue.hongshu                      D  queryAndSetGLESMaxVersion select gles-version: 3.1 hostGLVersion:46 process:com.xue.hongshu
2025-08-08 00:14:07.913  4316-4404  cr_media                com.xue.hongshu                      W  Requires BLUETOOTH permission
2025-08-08 00:14:07.929  4316-4414  EGL_emulation           com.xue.hongshu                      D  eglCreateContext: 0x763878c5e780: maj 3 min 1 rcv 4
2025-08-08 00:14:07.936  4316-4414  HostConnection          com.xue.hongshu                      D  ExtendedRCEncoderContext GL_VERSION return OpenGL ES 3.1 v1
2025-08-08 00:14:07.951  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000087fe
2025-08-08 00:14:07.979  4316-4347  EGL_emulation           com.xue.hongshu                      E  tid 4347: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:14:07.979  4316-4347  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x76388be27d80, error=EGL_BAD_MATCH
2025-08-08 00:14:07.990  4316-4414  EGL_emulation           com.xue.hongshu                      D  eglCreateContext: 0x763878c5e780: maj 3 min 0 rcv 3
2025-08-08 00:14:07.991  4316-4316  Choreographer           com.xue.hongshu                      I  Skipped 45 frames!  The application may be doing too much work on its main thread.
2025-08-08 00:14:07.996  4316-4414  HostConnection          com.xue.hongshu                      D  ExtendedRCEncoderContext GL_VERSION return OpenGL ES 3.1 v1
2025-08-08 00:14:08.007  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.012  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000087fe
2025-08-08 00:14:08.026  4316-4347  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.026  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.029  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.029  4316-4347  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000087fe
2025-08-08 00:14:08.029  4316-4347  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.031  4316-4347  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.034  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.035  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.044  4316-4347  OpenGLRenderer          com.xue.hongshu                      I  Davey! duration=811ms; Flags=0, IntendedVsync=5099683526236, Vsync=5100433526206, OldestInputEvent=9223372036854775807, NewestInputEvent=0, HandleInputStart=5100442453094, AnimationStart=5100442501012, PerformTraversalsStart=5100449391222, DrawStart=5100450095155, SyncQueued=5100462758933, SyncStart=5100463409723, IssueDrawCommandsStart=5100463498530, SwapBuffers=5100494821917, FrameCompleted=5100495850861, DequeueBufferDuration=60000, QueueBufferDuration=151000, 
2025-08-08 00:14:08.049  4316-4347  OpenGLRenderer          com.xue.hongshu                      I  Davey! duration=816ms; Flags=0, IntendedVsync=5099683526236, Vsync=5100433526206, OldestInputEvent=9223372036854775807, NewestInputEvent=0, HandleInputStart=5100442453094, AnimationStart=5100442501012, PerformTraversalsStart=5100449391222, DrawStart=5100496131069, SyncQueued=5100497960705, SyncStart=5100498311585, IssueDrawCommandsStart=5100498353710, SwapBuffers=5100499192530, FrameCompleted=5100500558337, DequeueBufferDuration=86000, QueueBufferDuration=158000, 
2025-08-08 00:14:08.385  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(73)] "Enhanced anti-detection script loaded", source:  (73)
2025-08-08 00:14:08.392  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:08.393  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(30)] "Bypass detection script loaded", source:  (30)
2025-08-08 00:14:08.571  4316-4396  NetworkSecurityConfig   com.xue.hongshu                      D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-08 00:14:08.880  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Page started loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:14:08.981  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(0)] "Mixed Content: The page at 'https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59' was loaded over HTTPS, but requested an insecure image 'http://sns-avatar-qc.xhscdn.com/user_banner/1040g2k031i1bh3qin0dg5p6hlv82ovqp82bj5eo?imageView2/2/w/540/format/jpg'. This content should also be served over HTTPS.", source: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59 (0)
2025-08-08 00:14:09.411  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "框架和 SDK 输出的日志默认不展示，可在框架配置文件中设置开启，详见 https://doc.weixin.qq.com/doc/w3_AWkASAb9APAr8IdJI5VS0OyqetUE6?scode=ANAAyQcbAAgB8qKjm9AWkASAb9APA", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:09.637  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-08 00:14:09.699  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:09.719  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 2 lines
2025-08-08 00:14:09.726  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:09.754  4316-4316  chromium                com.xue.hongshu                      E  [ERROR:web_contents_delegate.cc(225)] WebContentsDelegate::CheckMediaAccessPermission: Not supported.
2025-08-08 00:14:09.754  4316-4316  chromium                com.xue.hongshu                      E  [ERROR:web_contents_delegate.cc(225)] WebContentsDelegate::CheckMediaAccessPermission: Not supported.
2025-08-08 00:14:09.778  4316-4404  cr_media                com.xue.hongshu                      W  Requires MODIFY_AUDIO_SETTINGS and RECORD_AUDIO. No audio device will be available for recording
2025-08-08 00:14:09.787  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:14:09.787  4316-4416  CameraManagerGlobal     com.xue.hongshu                      I  Connecting to camera service
2025-08-08 00:14:09.788  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Waiting 3479ms before extracting data
2025-08-08 00:14:09.852  4316-4416  CameraBase              com.xue.hongshu                      W  An error occurred while connecting to camera 1: Status(-8): '1: validateClientPermissionsLocked:906: Caller "com.xue.hongshu" (PID 10052, UID 4316) cannot open camera "1" without camera permission'
2025-08-08 00:14:09.854  4316-4416  cr_VideoCapture         com.xue.hongshu                      E  Camera.open:  (Ask Gemini)
                                                                                                    java.lang.RuntimeException: Fail to connect to camera service
                                                                                                    	at android.hardware.Camera.<init>(Camera.java:546)
                                                                                                    	at android.hardware.Camera.open(Camera.java:392)
                                                                                                    	at org.chromium.media.VideoCaptureFactory.isZoomSupported(chromium-SystemWebView.apk-default-447211456:3)
2025-08-08 00:14:09.864  4316-4416  CameraBase              com.xue.hongshu                      W  An error occurred while connecting to camera 1: Status(-8): '1: validateClientPermissionsLocked:906: Caller "com.xue.hongshu" (PID 10052, UID 4316) cannot open camera "1" without camera permission'
2025-08-08 00:14:09.864  4316-4416  cr_VideoCapture         com.xue.hongshu                      E  Camera.open:  (Ask Gemini)
                                                                                                    java.lang.RuntimeException: Fail to connect to camera service
                                                                                                    	at android.hardware.Camera.<init>(Camera.java:546)
                                                                                                    	at android.hardware.Camera.open(Camera.java:392)
                                                                                                    	at org.chromium.media.VideoCaptureFactory.getDeviceSupportedFormats(chromium-SystemWebView.apk-default-447211456:3)
2025-08-08 00:14:09.894  4316-4416  CameraBase              com.xue.hongshu                      W  An error occurred while connecting to camera 0: Status(-8): '1: validateClientPermissionsLocked:906: Caller "com.xue.hongshu" (PID 10052, UID 4316) cannot open camera "0" without camera permission'
2025-08-08 00:14:09.894  4316-4416  cr_VideoCapture         com.xue.hongshu                      E  Camera.open:  (Ask Gemini)
                                                                                                    java.lang.RuntimeException: Fail to connect to camera service
                                                                                                    	at android.hardware.Camera.<init>(Camera.java:546)
                                                                                                    	at android.hardware.Camera.open(Camera.java:392)
                                                                                                    	at org.chromium.media.VideoCaptureFactory.isZoomSupported(chromium-SystemWebView.apk-default-447211456:3)
2025-08-08 00:14:09.908  4316-4416  CameraBase              com.xue.hongshu                      W  An error occurred while connecting to camera 0: Status(-8): '1: validateClientPermissionsLocked:906: Caller "com.xue.hongshu" (PID 10052, UID 4316) cannot open camera "0" without camera permission'
2025-08-08 00:14:09.908  4316-4416  cr_VideoCapture         com.xue.hongshu                      E  Camera.open:  (Ask Gemini)
                                                                                                    java.lang.RuntimeException: Fail to connect to camera service
                                                                                                    	at android.hardware.Camera.<init>(Camera.java:546)
                                                                                                    	at android.hardware.Camera.open(Camera.java:392)
                                                                                                    	at org.chromium.media.VideoCaptureFactory.getDeviceSupportedFormats(chromium-SystemWebView.apk-default-447211456:3)
2025-08-08 00:14:10.004  4316-4347  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:14:10.162  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:14:10.162  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Waiting 3333ms before extracting data
2025-08-08 00:14:10.298  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-08 00:14:10.335  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:10.383  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:14:10.383  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Waiting 3408ms before extracting data
2025-08-08 00:14:10.531  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-08 00:14:10.813  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.053  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 6 lines
2025-08-08 00:14:11.058  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.150  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.159  4316-4404  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:14:11.159  4316-4404  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:14:11.159  4316-4404  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:14:11.165  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.170  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.171  4316-4404  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:14:11.179  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.181  4316-4404  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:14:11.181  4316-4404  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:14:11.181  4316-4404  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:14:11.189  4316-4404  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:14:11.230  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.231  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 1 line
2025-08-08 00:14:11.236  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.240  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:14:11.240  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:14:11.241  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.276  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 8 lines
2025-08-08 00:14:11.282  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.314  4316-4414  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:14:11.325  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.326  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 1 line
2025-08-08 00:14:11.330  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.332  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:14:11.334  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:14:11.335  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.345  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 3 lines
2025-08-08 00:14:11.348  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.351  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.363  4316-4404  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:14:11.363  4316-4404  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:14:11.363  4316-4404  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:14:11.363  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.363  4316-4404  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:14:11.372  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.403  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:14:11.406  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.408  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:14:11.410  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:14:11.410  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.420  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:14:11.423  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.474  4316-4404  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:14:11.474  4316-4404  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:14:11.474  4316-4404  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:14:11.475  4316-4404  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:14:11.475  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.488  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 2 lines
2025-08-08 00:14:11.494  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.511  4316-4414  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:14:11.538  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.539  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 1 line
2025-08-08 00:14:11.543  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.546  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:14:11.548  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:14:11.549  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.561  4316-4414  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:14:11.564  4316-4414  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] pageView", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] visitMobile", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] AbTestPluginMetrics", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] AbTestPluginMetrics", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.579  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.635  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.638  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.655  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:11.660  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:13.285  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:14:13.288  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:14:13.496  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Already extracting, returning
2025-08-08 00:14:13.792  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Already extracting, returning
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.098  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.099  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.099  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.100  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.100  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.100  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.100  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.100  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.100  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.100  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.100  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserMemory", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserNetwork", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserNavigationTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserScriptsExecutionTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserRenderTimes", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.102  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:14.106  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:14:14.106  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:14:14.106  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:14:14.106  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:14:14.106  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(21)] "尝试触发懒加载...", source:  (21)
2025-08-08 00:14:14.107  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (65)
2025-08-08 00:14:14.107  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .user-name found 1 elements", source:  (69)
2025-08-08 00:14:14.107  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(74)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (74)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(85)] "Starting enhanced time search...", source:  (85)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (65)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .time-text found 0 elements", source:  (69)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-time found 0 elements", source:  (69)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-time found 0 elements", source:  (69)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .create-time found 0 elements", source:  (69)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [data-time] found 0 elements", source:  (69)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-time found 0 elements", source:  (69)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .timestamp found 0 elements", source:  (69)
2025-08-08 00:14:14.108  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector time found 0 elements", source:  (69)
2025-08-08 00:14:14.109  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .date found 0 elements", source:  (69)
2025-08-08 00:14:14.109  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-date found 0 elements", source:  (69)
2025-08-08 00:14:14.109  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .creation-time found 0 elements", source:  (69)
2025-08-08 00:14:14.109  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-date found 0 elements", source:  (69)
2025-08-08 00:14:14.109  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="time"] found 0 elements", source:  (69)
2025-08-08 00:14:14.109  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="date"] found 0 elements", source:  (69)
2025-08-08 00:14:14.110  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="publish"] found 0 elements", source:  (69)
2025-08-08 00:14:14.110  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-item time found 0 elements", source:  (69)
2025-08-08 00:14:14.110  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .feed-item time found 0 elements", source:  (69)
2025-08-08 00:14:14.110  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-item time found 0 elements", source:  (69)
2025-08-08 00:14:14.110  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No time found", source:  (79)
2025-08-08 00:14:14.110  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(92)] "Searching for time patterns in text content...", source:  (92)
2025-08-08 00:14:14.113  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(126)] "No time element found with text patterns", source:  (126)
2025-08-08 00:14:14.113  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(220)] "Searching for latest note time...", source:  (220)
2025-08-08 00:14:14.114  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(132)] "=== 开始页面结构分析 ===", source:  (132)
2025-08-08 00:14:14.115  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(144)] "总元素数量: 216", source:  (144)
2025-08-08 00:14:14.116  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(165)] "找到的时间元素: ", source:  (165)
2025-08-08 00:14:14.117  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(187)] "找到的容器: [object Object],[object Object],[object Object],[object Object],[object Object]", source:  (187)
2025-08-08 00:14:14.117  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(371)] "Script error: TypeError: element.className.toLowerCase is not a function", source:  (371)
2025-08-08 00:14:14.119  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:14:14.119  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":false,\"error\":\"element.className.toLowerCase is not a function\",\"errorType\":\"unknown\",\"suggestion\":\"\",\"stack\":\"TypeError: element.className.toLowerCase is not a function\\n    at analyzePageStructure (\u003Canonymous>:194:53)\\n    at findLatestNoteTime (\u003Canonymous>:223:39)\\n    at \u003Canonymous>:257:48\\n    at \u003Canonymous>:398:3\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754583254118}"
2025-08-08 00:14:14.121  4316-4316  XhsWebViewClient        com.xue.hongshu                      W  JavaScript execution failed: element.className.toLowerCase is not a function (type: unknown)
2025-08-08 00:14:14.121  4316-4316  XhsWebViewClient        com.xue.hongshu                      W  Retrying extraction (attempt 1): element.className.toLowerCase is not a function
2025-08-08 00:14:14.253  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://dhjaibcmgocbpegmejfphjhhpaphmkpp/is.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:14:14.254  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Ignoring debug-related error: net::ERR_CONNECTION_REFUSED for http://127.0.0.1:9222/json/version
2025-08-08 00:14:14.256  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://beeaddblkeeialcohiolkkoiifhgjooj/manifest.json. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:14:14.256  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://ddgooekaaihgnbfbgalfiooiicdnmnia/js/js-index.ts.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:14:14.256  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://keeelahekhhgkpaipdodgjnmgkfcdpde/inject.6070000c.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:14:14.256  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://dbichmdlbjdeplpkhcejgkakobjbjalc/content-scripts/xiaohongshu.css. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:14:14.256  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://piejlhcmefdepbgalcongckfomfobokb/worker.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:14:14.256  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://lenndnnbdichpjlfmfadcjpaenmiflan/inject.bundle.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:14:14.256  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://hmeohemhimcjlegdjloglnkfablbneif/inject.bundle.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:14:14.256  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Ignoring debug-related error: net::ERR_CONNECTION_REFUSED for http://127.0.0.1:54345/
2025-08-08 00:14:14.408  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(29)] "滚动完成", source:  (29)
2025-08-08 00:14:15.121  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:14:15.121  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:14:15.123  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:14:15.123  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:14:15.123  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:14:15.123  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:14:15.123  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(21)] "尝试触发懒加载...", source:  (21)
2025-08-08 00:14:15.123  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (65)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .user-name found 1 elements", source:  (69)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(74)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (74)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(85)] "Starting enhanced time search...", source:  (85)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (65)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .time-text found 0 elements", source:  (69)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-time found 0 elements", source:  (69)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-time found 0 elements", source:  (69)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .create-time found 0 elements", source:  (69)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [data-time] found 0 elements", source:  (69)
2025-08-08 00:14:15.124  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-time found 0 elements", source:  (69)
2025-08-08 00:14:15.125  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .timestamp found 0 elements", source:  (69)
2025-08-08 00:14:15.125  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector time found 0 elements", source:  (69)
2025-08-08 00:14:15.125  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .date found 0 elements", source:  (69)
2025-08-08 00:14:15.125  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-date found 0 elements", source:  (69)
2025-08-08 00:14:15.125  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .creation-time found 0 elements", source:  (69)
2025-08-08 00:14:15.125  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-date found 0 elements", source:  (69)
2025-08-08 00:14:15.125  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="time"] found 0 elements", source:  (69)
2025-08-08 00:14:15.125  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="date"] found 0 elements", source:  (69)
2025-08-08 00:14:15.126  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="publish"] found 0 elements", source:  (69)
2025-08-08 00:14:15.126  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-item time found 0 elements", source:  (69)
2025-08-08 00:14:15.126  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .feed-item time found 0 elements", source:  (69)
2025-08-08 00:14:15.126  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-item time found 0 elements", source:  (69)
2025-08-08 00:14:15.126  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No time found", source:  (79)
2025-08-08 00:14:15.126  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(92)] "Searching for time patterns in text content...", source:  (92)
2025-08-08 00:14:15.127  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(126)] "No time element found with text patterns", source:  (126)
2025-08-08 00:14:15.127  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(220)] "Searching for latest note time...", source:  (220)
2025-08-08 00:14:15.127  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(132)] "=== 开始页面结构分析 ===", source:  (132)
2025-08-08 00:14:15.127  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(144)] "总元素数量: 216", source:  (144)
2025-08-08 00:14:15.129  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(165)] "找到的时间元素: ", source:  (165)
2025-08-08 00:14:15.130  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(187)] "找到的容器: [object Object],[object Object],[object Object],[object Object],[object Object]", source:  (187)
2025-08-08 00:14:15.131  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(371)] "Script error: TypeError: element.className.toLowerCase is not a function", source:  (371)
2025-08-08 00:14:15.131  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:14:15.131  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":false,\"error\":\"element.className.toLowerCase is not a function\",\"errorType\":\"unknown\",\"suggestion\":\"\",\"stack\":\"TypeError: element.className.toLowerCase is not a function\\n    at analyzePageStructure (\u003Canonymous>:194:53)\\n    at findLatestNoteTime (\u003Canonymous>:223:39)\\n    at \u003Canonymous>:257:48\\n    at \u003Canonymous>:398:3\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754583255130}"
2025-08-08 00:14:15.131  4316-4316  XhsWebViewClient        com.xue.hongshu                      W  JavaScript execution failed: element.className.toLowerCase is not a function (type: unknown)
2025-08-08 00:14:15.131  4316-4316  XhsWebViewClient        com.xue.hongshu                      W  Retrying extraction (attempt 2): element.className.toLowerCase is not a function
2025-08-08 00:14:15.429  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(29)] "滚动完成", source:  (29)
2025-08-08 00:14:16.625  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:16.625  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:16.625  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:16.625  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:14:17.134  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:14:17.134  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(21)] "尝试触发懒加载...", source:  (21)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (65)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .user-name found 1 elements", source:  (69)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(74)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (74)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(85)] "Starting enhanced time search...", source:  (85)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (65)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .time-text found 0 elements", source:  (69)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-time found 0 elements", source:  (69)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-time found 0 elements", source:  (69)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .create-time found 0 elements", source:  (69)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [data-time] found 0 elements", source:  (69)
2025-08-08 00:14:17.135  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-time found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .timestamp found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector time found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .date found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-date found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .creation-time found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-date found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="time"] found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="date"] found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="publish"] found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-item time found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .feed-item time found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-item time found 0 elements", source:  (69)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No time found", source:  (79)
2025-08-08 00:14:17.136  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(92)] "Searching for time patterns in text content...", source:  (92)
2025-08-08 00:14:17.137  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(126)] "No time element found with text patterns", source:  (126)
2025-08-08 00:14:17.137  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(220)] "Searching for latest note time...", source:  (220)
2025-08-08 00:14:17.137  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(132)] "=== 开始页面结构分析 ===", source:  (132)
2025-08-08 00:14:17.137  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(144)] "总元素数量: 216", source:  (144)
2025-08-08 00:14:17.139  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(165)] "找到的时间元素: ", source:  (165)
2025-08-08 00:14:17.139  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(187)] "找到的容器: [object Object],[object Object],[object Object],[object Object],[object Object]", source:  (187)
2025-08-08 00:14:17.140  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(371)] "Script error: TypeError: element.className.toLowerCase is not a function", source:  (371)
2025-08-08 00:14:17.140  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:14:17.140  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":false,\"error\":\"element.className.toLowerCase is not a function\",\"errorType\":\"unknown\",\"suggestion\":\"\",\"stack\":\"TypeError: element.className.toLowerCase is not a function\\n    at analyzePageStructure (\u003Canonymous>:194:53)\\n    at findLatestNoteTime (\u003Canonymous>:223:39)\\n    at \u003Canonymous>:257:48\\n    at \u003Canonymous>:398:3\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754583257139}"
2025-08-08 00:14:17.140  4316-4316  XhsWebViewClient        com.xue.hongshu                      W  JavaScript execution failed: element.className.toLowerCase is not a function (type: unknown)
2025-08-08 00:14:17.140  4316-4316  XhsWebViewClient        com.xue.hongshu                      W  Retrying extraction (attempt 3): element.className.toLowerCase is not a function
2025-08-08 00:14:17.437  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(29)] "滚动完成", source:  (29)
2025-08-08 00:14:20.140  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:14:20.140  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:14:20.142  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:14:20.142  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(21)] "尝试触发懒加载...", source:  (21)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (65)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .user-name found 1 elements", source:  (69)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(74)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (74)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(85)] "Starting enhanced time search...", source:  (85)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(65)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (65)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .time-text found 0 elements", source:  (69)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-time found 0 elements", source:  (69)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-time found 0 elements", source:  (69)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .create-time found 0 elements", source:  (69)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [data-time] found 0 elements", source:  (69)
2025-08-08 00:14:20.143  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-time found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .timestamp found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector time found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .date found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .publish-date found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .creation-time found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-date found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="time"] found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="date"] found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector [class*="publish"] found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .note-item time found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .feed-item time found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(69)] "Selector .post-item time found 0 elements", source:  (69)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(79)] "No time found", source:  (79)
2025-08-08 00:14:20.144  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(92)] "Searching for time patterns in text content...", source:  (92)
2025-08-08 00:14:20.145  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(126)] "No time element found with text patterns", source:  (126)
2025-08-08 00:14:20.145  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(220)] "Searching for latest note time...", source:  (220)
2025-08-08 00:14:20.145  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(132)] "=== 开始页面结构分析 ===", source:  (132)
2025-08-08 00:14:20.145  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(144)] "总元素数量: 216", source:  (144)
2025-08-08 00:14:20.146  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(165)] "找到的时间元素: ", source:  (165)
2025-08-08 00:14:20.146  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(187)] "找到的容器: [object Object],[object Object],[object Object],[object Object],[object Object]", source:  (187)
2025-08-08 00:14:20.155  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(371)] "Script error: TypeError: element.className.toLowerCase is not a function", source:  (371)
2025-08-08 00:14:20.155  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:14:20.155  4316-4316  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":false,\"error\":\"element.className.toLowerCase is not a function\",\"errorType\":\"unknown\",\"suggestion\":\"\",\"stack\":\"TypeError: element.className.toLowerCase is not a function\\n    at analyzePageStructure (\u003Canonymous>:194:53)\\n    at findLatestNoteTime (\u003Canonymous>:223:39)\\n    at \u003Canonymous>:257:48\\n    at \u003Canonymous>:398:3\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754583260155}"
2025-08-08 00:14:20.155  4316-4316  XhsWebViewClient        com.xue.hongshu                      W  JavaScript execution failed: element.className.toLowerCase is not a function (type: unknown)
2025-08-08 00:14:20.159  4316-4316  ErrorHandler            com.xue.hongshu                      D  Error stats: {PARSING_ERROR=1}
2025-08-08 00:14:20.159  4316-4316  XhsWebViewClient        com.xue.hongshu                      E  Final error after 3 retries: 数据格式异常，可能是页面结构发生变化
2025-08-08 00:14:20.163  4316-4316  DataExtraction          com.xue.hongshu                      W  数据抓取: 员工ID=fb69f122-84a9-43ba-93f6-6de78f6c2fb8, 结果=失败, 详情=数据格式异常，可能是页面结构发生变化
2025-08-08 00:14:20.200  4316-4316  System.err              com.xue.hongshu                      W  java.lang.Exception: Toast callstack! strTip=检查失败: 数据格式异常，可能是页面结构发生变化
2025-08-08 00:14:20.200  4316-4316  System.err              com.xue.hongshu                      W  
2025-08-08 00:14:20.200  4316-4316  System.err              com.xue.hongshu                      W  建议:
2025-08-08 00:14:20.200  4316-4316  System.err              com.xue.hongshu                      W  1. 检查网络连接
2025-08-08 00:14:20.200  4316-4316  System.err              com.xue.hongshu                      W  2. 确认用户ID正确
2025-08-08 00:14:20.200  4316-4316  System.err              com.xue.hongshu                      W  3. 稍后重试
2025-08-08 00:14:20.202  4316-4316  System.err              com.xue.hongshu                      W  	at android.widget.Toast.show(Toast.java:143)
2025-08-08 00:14:20.202  4316-4316  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleError$1.invokeSuspend(WebViewActivity.kt:121)
2025-08-08 00:14:20.202  4316-4316  System.err              com.xue.hongshu                      W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-08-08 00:14:20.202  4316-4316  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
2025-08-08 00:14:20.202  4316-4316  System.err              com.xue.hongshu                      W  	at android.os.Handler.handleCallback(Handler.java:873)
2025-08-08 00:14:20.202  4316-4316  System.err              com.xue.hongshu                      W  	at android.os.Handler.dispatchMessage(Handler.java:99)
2025-08-08 00:14:20.202  4316-4316  System.err              com.xue.hongshu                      W  	at android.os.Looper.loop(Looper.java:193)
2025-08-08 00:14:20.202  4316-4316  System.err              com.xue.hongshu                      W  	at android.app.ActivityThread.main(ActivityThread.java:6834)
2025-08-08 00:14:20.204  4316-4316  System.err              com.xue.hongshu                      W  	at java.lang.reflect.Method.invoke(Native Method)
2025-08-08 00:14:20.204  4316-4316  System.err              com.xue.hongshu                      W  	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
2025-08-08 00:14:20.204  4316-4316  System.err              com.xue.hongshu                      W  	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-08-08 00:14:20.271  4316-4347  EGL_emulation           com.xue.hongshu                      E  tid 4347: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:14:20.271  4316-4347  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878dc9a00, error=EGL_BAD_MATCH
2025-08-08 00:14:20.451  4316-4316  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(29)] "滚动完成", source:  (29)
2025-08-08 00:14:41.199  4316-4404  <no-tag>                com.xue.hongshu                      D  PlayerBase::stop() from IPlayer
2025-08-08 00:14:41.199  4316-4404  AudioTrack              com.xue.hongshu                      D  stop() called with 1430400 frames delivered
2025-08-08 00:14:41.202  4316-4404  <no-tag>                com.xue.hongshu                      D  PlayerBase::stop() from IPlayer
2025-08-08 00:14:41.202  4316-4404  AudioTrack              com.xue.hongshu                      D  stop() called with 1430400 frames delivered
2025-08-08 00:14:41.369  4316-4404  <no-tag>                com.xue.hongshu                      D  PlayerBase::stop() from IPlayer
2025-08-08 00:14:41.369  4316-4404  AudioTrack              com.xue.hongshu                      D  stop() called with 1430532 frames delivered
2025-08-08 00:14:41.497  4316-4404  <no-tag>                com.xue.hongshu                      D  PlayerBase::stop() from IPlayer
2025-08-08 00:14:41.497  4316-4404  AudioTrack              com.xue.hongshu                      D  stop() called with 1432580 frames delivered