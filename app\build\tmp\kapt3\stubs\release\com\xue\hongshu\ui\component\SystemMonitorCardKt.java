package com.xue.hongshu.ui.component;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000d\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u00060\u0005H\u0003\u001a*\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\f\u0010\r\u001av\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u001a0\u00182\b\b\u0002\u0010\u001b\u001a\u00020\u001c2\u000e\b\u0002\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u001e2\u000e\b\u0002\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u001e2\u000e\b\u0002\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u001eH\u0007\u001a\u0010\u0010!\u001a\u00020\u00032\u0006\u0010\"\u001a\u00020\u0014H\u0002\u001a\u0015\u0010#\u001a\u00020\u000b2\u0006\u0010$\u001a\u00020%H\u0003\u00a2\u0006\u0002\u0010&\u001a\u0010\u0010\'\u001a\u00020\u00032\u0006\u0010$\u001a\u00020%H\u0002\u001a\u0010\u0010(\u001a\u00020\u00032\u0006\u0010)\u001a\u00020\u0019H\u0002\u001a\u0015\u0010*\u001a\u00020\u000b2\u0006\u0010\"\u001a\u00020\u0014H\u0003\u00a2\u0006\u0002\u0010+\u001a\u0010\u0010,\u001a\u00020\u00032\u0006\u0010-\u001a\u00020\u0012H\u0002\u001a\u0010\u0010.\u001a\u00020\u00032\u0006\u0010/\u001a\u000200H\u0002\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00061"}, d2 = {"DetailSection", "", "title", "", "items", "", "Lkotlin/Pair;", "StatusItem", "label", "value", "color", "Landroidx/compose/ui/graphics/Color;", "StatusItem-mxwnekA", "(Ljava/lang/String;Ljava/lang/String;J)V", "SystemMonitorCard", "configManager", "Lcom/xue/hongshu/utils/ConfigManager;", "networkState", "Lcom/xue/hongshu/utils/NetworkState;", "connectionQuality", "Lcom/xue/hongshu/utils/ConnectionQuality;", "logStats", "Lcom/xue/hongshu/utils/LogStats;", "errorStats", "", "Lcom/xue/hongshu/utils/ErrorHandler$ErrorType;", "", "modifier", "Landroidx/compose/ui/Modifier;", "onViewLogs", "Lkotlin/Function0;", "onClearLogs", "onResetConfig", "getConnectionQualityText", "quality", "getDetectionLevelColor", "level", "Lcom/xue/hongshu/utils/ConfigManager$DetectionLevel;", "(Lcom/xue/hongshu/utils/ConfigManager$DetectionLevel;)J", "getDetectionLevelText", "getErrorTypeText", "type", "getNetworkColor", "(Lcom/xue/hongshu/utils/ConnectionQuality;)J", "getNetworkDisplayText", "state", "getStrategyModeText", "mode", "Lcom/xue/hongshu/utils/ConfigManager$StrategyMode;", "app_release"})
public final class SystemMonitorCardKt {
    
    @androidx.compose.runtime.Composable()
    public static final void SystemMonitorCard(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.ConfigManager configManager, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.NetworkState networkState, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.ConnectionQuality connectionQuality, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.LogStats logStats, @org.jetbrains.annotations.NotNull()
    java.util.Map<com.xue.hongshu.utils.ErrorHandler.ErrorType, java.lang.Integer> errorStats, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onViewLogs, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClearLogs, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onResetConfig) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DetailSection(java.lang.String title, java.util.List<kotlin.Pair<java.lang.String, java.lang.String>> items) {
    }
    
    private static final java.lang.String getNetworkDisplayText(com.xue.hongshu.utils.NetworkState state) {
        return null;
    }
    
    private static final java.lang.String getConnectionQualityText(com.xue.hongshu.utils.ConnectionQuality quality) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    private static final long getNetworkColor(com.xue.hongshu.utils.ConnectionQuality quality) {
        return 0L;
    }
    
    private static final java.lang.String getDetectionLevelText(com.xue.hongshu.utils.ConfigManager.DetectionLevel level) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    private static final long getDetectionLevelColor(com.xue.hongshu.utils.ConfigManager.DetectionLevel level) {
        return 0L;
    }
    
    private static final java.lang.String getStrategyModeText(com.xue.hongshu.utils.ConfigManager.StrategyMode mode) {
        return null;
    }
    
    private static final java.lang.String getErrorTypeText(com.xue.hongshu.utils.ErrorHandler.ErrorType type) {
        return null;
    }
}