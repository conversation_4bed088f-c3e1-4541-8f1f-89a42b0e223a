package com.xue.hongshu.webview

import android.webkit.WebSettings
import android.webkit.WebView
import kotlin.random.Random

object AntiDetectionHelper {
    
    private val userAgents = listOf(
        // Android Chrome 最新版本
        "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; Pixel 7 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 12; OnePlus 10 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; Xiaomi 13) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",

        // iPhone Safari 最新版本
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",

        // 微信内置浏览器
        "Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.0.0 Mobile Safari/537.36 MicroMessenger/8.0.47.2560(0x28002F30) Process/tools WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.47(0x18002f2f) NetType/WIFI Language/zh_CN",

        // 小红书APP内置浏览器模拟
        "Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.0.0 Mobile Safari/537.36 XhsApp/8.25.0",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 XhsApp/8.25.0"
    )
    
    fun configureWebView(webView: WebView) {
        val settings = webView.settings
        
        // 基础设置
        settings.javaScriptEnabled = true
        settings.domStorageEnabled = true
        // Note: databaseEnabled is deprecated in API 33+, but DOM storage provides similar functionality
        settings.cacheMode = WebSettings.LOAD_DEFAULT
        
        // 反检测设置
        settings.userAgentString = getRandomUserAgent()
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW

        // SSL和安全设置
        // Note: allowUniversalAccessFromFileURLs and allowFileAccessFromFileURLs are deprecated in API 33+
        // These settings are now controlled by the network security config and are disabled by default for security
        settings.allowFileAccess = false
        settings.allowContentAccess = false
        
        // 模拟真实浏览器行为
        settings.setSupportZoom(true)
        settings.builtInZoomControls = true
        settings.displayZoomControls = false
        settings.loadWithOverviewMode = true
        settings.useWideViewPort = true
        
        // 禁用一些可能被检测的功能
        settings.setGeolocationEnabled(false)
        

    }
    
    fun getRandomUserAgent(): String {
        return userAgents[Random.nextInt(userAgents.size)]
    }
    
    fun getRandomDelay(): Long {
        // 随机延迟2-8秒，更符合真实用户行为
        return Random.nextLong(2000, 8000)
    }

    fun getRandomScrollDelay(): Long {
        // 滚动间隔随机延迟
        return Random.nextLong(500, 2000)
    }

    fun getRandomClickDelay(): Long {
        // 点击延迟
        return Random.nextLong(300, 1500)
    }
    
    fun injectAntiDetectionScript(webView: WebView) {
        val script = """
            (function() {
                // 隐藏WebView和自动化特征
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // 删除可能的自动化标识
                delete window.webdriver;
                delete window._phantom;
                delete window.__nightmare;
                delete window._selenium;
                delete window.callPhantom;
                delete window.callSelenium;
                delete window._Selenium_IDE_Recorder;

                // 模拟真实的屏幕信息
                Object.defineProperty(screen, 'availHeight', {
                    get: () => window.innerHeight,
                });

                Object.defineProperty(screen, 'availWidth', {
                    get: () => window.innerWidth,
                });

                // 模拟Chrome环境
                window.chrome = {
                    runtime: {
                        onConnect: undefined,
                        onMessage: undefined
                    },
                    app: {
                        isInstalled: false
                    }
                };

                // 模拟真实的插件信息
                Object.defineProperty(navigator, 'plugins', {
                    get: () => ({
                        length: 3,
                        0: { name: 'Chrome PDF Plugin' },
                        1: { name: 'Chrome PDF Viewer' },
                        2: { name: 'Native Client' }
                    }),
                });

                // 模拟真实的语言设置
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en-US', 'en'],
                });

                // 模拟真实的连接信息
                Object.defineProperty(navigator, 'connection', {
                    get: () => ({
                        effectiveType: '4g',
                        rtt: 50,
                        downlink: 10
                    }),
                });

                // 模拟真实的权限API
                Object.defineProperty(navigator, 'permissions', {
                    get: () => ({
                        query: () => Promise.resolve({ state: 'granted' })
                    }),
                });

                // 重写console.debug避免检测
                console.debug = () => {};

                // 模拟真实的触摸事件
                window.TouchEvent = window.TouchEvent || function() {};

                console.log('Enhanced anti-detection script loaded');
            })();
        """.trimIndent()

        webView.evaluateJavascript(script, null)
    }
    
    /**
     * 生成随机的访问间隔
     */
    fun getRandomInterval(): Long {
        // 5-15分钟的随机间隔
        return Random.nextLong(5 * 60 * 1000, 15 * 60 * 1000)
    }
    
    /**
     * 模拟人类滚动行为
     */
    fun simulateHumanScroll(webView: WebView) {
        val scrollScript = """
            (function() {
                let scrollCount = 0;
                const maxScrolls = Math.floor(Math.random() * 3) + 1;
                
                function randomScroll() {
                    if (scrollCount >= maxScrolls) return;
                    
                    const scrollAmount = Math.floor(Math.random() * 300) + 100;
                    window.scrollBy(0, scrollAmount);
                    scrollCount++;
                    
                    setTimeout(randomScroll, Math.random() * 1000 + 500);
                }
                
                setTimeout(randomScroll, Math.random() * 2000 + 1000);
            })();
        """.trimIndent()
        
        webView.evaluateJavascript(scrollScript, null)
    }

    /**
     * 模拟鼠标移动轨迹
     */
    fun simulateMouseMovement(webView: WebView) {
        val mouseScript = """
            (function() {
                let mouseX = Math.random() * window.innerWidth;
                let mouseY = Math.random() * window.innerHeight;

                function moveMouseRandomly() {
                    mouseX += (Math.random() - 0.5) * 50;
                    mouseY += (Math.random() - 0.5) * 50;

                    mouseX = Math.max(0, Math.min(window.innerWidth, mouseX));
                    mouseY = Math.max(0, Math.min(window.innerHeight, mouseY));

                    const event = new MouseEvent('mousemove', {
                        clientX: mouseX,
                        clientY: mouseY,
                        bubbles: true
                    });
                    document.dispatchEvent(event);
                }

                // 随机间隔移动鼠标
                setInterval(moveMouseRandomly, Math.random() * 3000 + 1000);
            })();
        """.trimIndent()

        webView.evaluateJavascript(mouseScript, null)
    }

    /**
     * 添加随机的页面交互
     */
    fun addRandomInteractions(webView: WebView) {
        val interactionScript = """
            (function() {
                // 随机点击空白区域
                function randomClick() {
                    const x = Math.random() * window.innerWidth;
                    const y = Math.random() * window.innerHeight;

                    const event = new MouseEvent('click', {
                        clientX: x,
                        clientY: y,
                        bubbles: true
                    });

                    // 只在空白区域点击，避免误触功能按钮
                    const element = document.elementFromPoint(x, y);
                    if (element && element.tagName === 'BODY') {
                        element.dispatchEvent(event);
                    }
                }

                // 随机键盘事件
                function randomKeyPress() {
                    const keys = ['ArrowDown', 'ArrowUp', 'Space'];
                    const key = keys[Math.floor(Math.random() * keys.length)];

                    const event = new KeyboardEvent('keydown', {
                        key: key,
                        bubbles: true
                    });
                    document.dispatchEvent(event);
                }

                // 随机执行交互
                setTimeout(() => {
                    if (Math.random() > 0.7) randomClick();
                    if (Math.random() > 0.8) randomKeyPress();
                }, Math.random() * 5000 + 2000);
            })();
        """.trimIndent()

        webView.evaluateJavascript(interactionScript, null)
    }

    /**
     * 检测并绕过常见的反爬虫检测
     */
    fun bypassCommonDetection(webView: WebView) {
        val bypassScript = """
            (function() {
                // 绕过常见的时间检测
                const originalDate = Date;
                Date = function(...args) {
                    if (args.length === 0) {
                        return new originalDate(originalDate.now() + Math.random() * 1000);
                    }
                    return new originalDate(...args);
                };
                Date.now = () => originalDate.now() + Math.random() * 1000;

                // 绕过性能检测
                if (window.performance && window.performance.now) {
                    const originalNow = window.performance.now;
                    window.performance.now = function() {
                        return originalNow.call(this) + Math.random() * 10;
                    };
                }

                // 模拟真实的网络延迟
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    return new Promise((resolve) => {
                        setTimeout(() => {
                            resolve(originalFetch.apply(this, args));
                        }, Math.random() * 100 + 50);
                    });
                };

                console.log('Bypass detection script loaded');
            })();
        """.trimIndent()

        webView.evaluateJavascript(bypassScript, null)
    }
}
