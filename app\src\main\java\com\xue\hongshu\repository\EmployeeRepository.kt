package com.xue.hongshu.repository

import com.xue.hongshu.data.dao.EmployeeDao
import com.xue.hongshu.data.entity.Employee
import com.xue.hongshu.data.entity.EmployeeStatus
import com.xue.hongshu.data.entity.EmployeeWithStats
import com.xue.hongshu.webview.XhsUserData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

class EmployeeRepository(private val employeeDao: EmployeeDao) {
    
    fun getAllEmployees(): Flow<List<Employee>> = employeeDao.getAllEmployees()
    
    fun getEmployeesWithStats(): Flow<List<EmployeeWithStats>> {
        return employeeDao.getAllEmployees().map { employees ->
            employees.map { employee ->
                val daysSinceLastPost = calculateDaysSinceLastPost(employee.lastPostTime)
                val isOverdue = daysSinceLastPost > EmployeeWithStats.OVERDUE_DAYS
                EmployeeWithStats(employee, daysSinceLastPost, isOverdue)
            }
        }
    }
    
    fun getEmployeesByStatus(status: EmployeeStatus): Flow<List<Employee>> = 
        employeeDao.getEmployeesByStatus(status)
    
    fun getOverdueEmployees(): Flow<List<Employee>> {
        val threshold = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(EmployeeWithStats.OVERDUE_DAYS.toLong())
        return employeeDao.getOverdueEmployees(threshold)
    }
    
    suspend fun getEmployeeById(id: String): Employee? = employeeDao.getEmployeeById(id)
    
    suspend fun addEmployee(employee: Employee) = employeeDao.insertEmployee(employee)
    
    suspend fun addEmployees(employees: List<Employee>) = employeeDao.insertEmployees(employees)
    
    suspend fun updateEmployee(employee: Employee) = employeeDao.updateEmployee(employee)
    
    suspend fun deleteEmployee(employee: Employee) = employeeDao.deleteEmployee(employee)
    
    suspend fun deleteEmployeeById(id: String) = employeeDao.deleteEmployeeById(id)
    
    suspend fun updateEmployeeStatus(id: String, status: EmployeeStatus) {
        employeeDao.updateEmployeeStatus(id, status, System.currentTimeMillis())
    }
    
    suspend fun updateEmployeeWithXhsData(employeeId: String, xhsData: XhsUserData) {
        val postTime = parseTimeString(xhsData.lastPostTime)
        employeeDao.updateEmployeePostInfo(
            id = employeeId,
            postTime = postTime,
            title = xhsData.lastPostTitle,
            status = EmployeeStatus.ACTIVE,
            checkTime = System.currentTimeMillis()
        )
    }
    
    suspend fun updateEmployeeError(id: String, errorMessage: String) {
        employeeDao.updateEmployeeError(id, errorMessage, EmployeeStatus.ERROR, System.currentTimeMillis())
    }
    
    suspend fun getEmployeeCount(): Int = employeeDao.getEmployeeCount()
    
    suspend fun getEmployeeCountByStatus(status: EmployeeStatus): Int = 
        employeeDao.getEmployeeCountByStatus(status)
    
    private fun calculateDaysSinceLastPost(lastPostTime: Long): Int {
        if (lastPostTime == 0L) return Int.MAX_VALUE
        
        val currentTime = System.currentTimeMillis()
        val diffInMillis = currentTime - lastPostTime
        return TimeUnit.MILLISECONDS.toDays(diffInMillis).toInt()
    }
    
    private fun parseTimeString(timeString: String): Long {
        if (timeString.isEmpty()) return 0L
        
        return try {
            when {
                timeString.contains("分钟前") -> {
                    val minutes = timeString.replace("分钟前", "").toIntOrNull() ?: 0
                    System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(minutes.toLong())
                }
                timeString.contains("小时前") -> {
                    val hours = timeString.replace("小时前", "").toIntOrNull() ?: 0
                    System.currentTimeMillis() - TimeUnit.HOURS.toMillis(hours.toLong())
                }
                timeString.contains("天前") -> {
                    val days = timeString.replace("天前", "").toIntOrNull() ?: 0
                    System.currentTimeMillis() - TimeUnit.DAYS.toMillis(days.toLong())
                }
                timeString.contains("昨天") -> {
                    System.currentTimeMillis() - TimeUnit.DAYS.toMillis(1)
                }
                timeString.contains("前天") -> {
                    System.currentTimeMillis() - TimeUnit.DAYS.toMillis(2)
                }
                timeString.matches(Regex("\\d{4}-\\d{2}-\\d{2}")) -> {
                    // 格式：2024-01-01
                    val format = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    format.parse(timeString)?.time ?: 0L
                }
                timeString.matches(Regex("\\d{2}-\\d{2}")) -> {
                    // 格式：01-01
                    val currentYear = Calendar.getInstance().get(Calendar.YEAR)
                    val format = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    format.parse("$currentYear-$timeString")?.time ?: 0L
                }
                else -> {
                    // 尝试其他常见格式
                    val formats = listOf(
                        "yyyy年MM月dd日",
                        "MM月dd日",
                        "yyyy/MM/dd",
                        "MM/dd"
                    )
                    
                    for (formatPattern in formats) {
                        try {
                            val format = SimpleDateFormat(formatPattern, Locale.getDefault())
                            val date = format.parse(timeString)
                            if (date != null) {
                                return date.time
                            }
                        } catch (e: Exception) {
                            continue
                        }
                    }
                    
                    0L // 无法解析时返回0
                }
            }
        } catch (e: Exception) {
            0L
        }
    }
}
