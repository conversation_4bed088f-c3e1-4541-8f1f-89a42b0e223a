package com.xue.hongshu.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u0015J\u0006\u0010\u0018\u001a\u00020\u0013J\u0006\u0010\u0019\u001a\u00020\u0013J\u000e\u0010\u001a\u001a\u00020\u00132\u0006\u0010\u001b\u001a\u00020\u001cJ\b\u0010\u001d\u001a\u00020\u0013H\u0002J\u0016\u0010\u001e\u001a\u00020\u00132\u0006\u0010\u001f\u001a\u00020\u00152\u0006\u0010 \u001a\u00020\u0015J\u0016\u0010!\u001a\u00020\u00132\u0006\u0010\u001f\u001a\u00020\u00152\u0006\u0010\"\u001a\u00020#J\u000e\u0010$\u001a\u00020\u00132\u0006\u0010%\u001a\u00020&J\u0006\u0010\'\u001a\u00020\u0013J\u000e\u0010(\u001a\u00020\u00132\u0006\u0010\u001f\u001a\u00020\u0015R\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000f\u00a8\u0006)"}, d2 = {"Lcom/xue/hongshu/viewmodel/MainViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Lcom/xue/hongshu/repository/EmployeeRepository;", "(Lcom/xue/hongshu/repository/EmployeeRepository;)V", "_employees", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/xue/hongshu/data/entity/EmployeeWithStats;", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/xue/hongshu/viewmodel/MainUiState;", "employees", "Lkotlinx/coroutines/flow/StateFlow;", "getEmployees", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "addEmployee", "", "name", "", "xhsUserId", "xhsUserName", "clearError", "clearMessage", "deleteEmployee", "employee", "Lcom/xue/hongshu/data/entity/Employee;", "loadStatistics", "onEmployeeCheckError", "employeeId", "errorMessage", "onEmployeeDataExtracted", "xhsData", "Lcom/xue/hongshu/webview/XhsUserData;", "setLoading", "isLoading", "", "startBatchCheck", "startCheckingEmployee", "app_release"})
public final class MainViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.xue.hongshu.repository.EmployeeRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.xue.hongshu.viewmodel.MainUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.xue.hongshu.viewmodel.MainUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.util.List<com.xue.hongshu.data.entity.EmployeeWithStats>> _employees = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.xue.hongshu.data.entity.EmployeeWithStats>> employees = null;
    
    public MainViewModel(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.repository.EmployeeRepository repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.xue.hongshu.viewmodel.MainUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.xue.hongshu.data.entity.EmployeeWithStats>> getEmployees() {
        return null;
    }
    
    public final void addEmployee(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String xhsUserId, @org.jetbrains.annotations.NotNull()
    java.lang.String xhsUserName) {
    }
    
    public final void deleteEmployee(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee) {
    }
    
    public final void startCheckingEmployee(@org.jetbrains.annotations.NotNull()
    java.lang.String employeeId) {
    }
    
    public final void onEmployeeDataExtracted(@org.jetbrains.annotations.NotNull()
    java.lang.String employeeId, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.webview.XhsUserData xhsData) {
    }
    
    public final void onEmployeeCheckError(@org.jetbrains.annotations.NotNull()
    java.lang.String employeeId, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage) {
    }
    
    public final void startBatchCheck() {
    }
    
    private final void loadStatistics() {
    }
    
    public final void clearMessage() {
    }
    
    public final void clearError() {
    }
    
    public final void setLoading(boolean isLoading) {
    }
}