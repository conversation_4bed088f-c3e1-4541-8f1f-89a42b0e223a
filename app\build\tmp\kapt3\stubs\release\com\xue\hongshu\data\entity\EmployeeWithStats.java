package com.xue.hongshu.data.entity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000e\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0087\b\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00072\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\r\u00a8\u0006\u0018"}, d2 = {"Lcom/xue/hongshu/data/entity/EmployeeWithStats;", "", "employee", "Lcom/xue/hongshu/data/entity/Employee;", "daysSinceLastPost", "", "isOverdue", "", "(Lcom/xue/hongshu/data/entity/Employee;IZ)V", "getDaysSinceLastPost", "()I", "getEmployee", "()Lcom/xue/hongshu/data/entity/Employee;", "()Z", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "toString", "", "Companion", "app_release"})
public final class EmployeeWithStats {
    @org.jetbrains.annotations.NotNull()
    private final com.xue.hongshu.data.entity.Employee employee = null;
    private final int daysSinceLastPost = 0;
    private final boolean isOverdue = false;
    public static final int OVERDUE_DAYS = 7;
    @org.jetbrains.annotations.NotNull()
    public static final com.xue.hongshu.data.entity.EmployeeWithStats.Companion Companion = null;
    
    public EmployeeWithStats(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee, int daysSinceLastPost, boolean isOverdue) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.data.entity.Employee getEmployee() {
        return null;
    }
    
    public final int getDaysSinceLastPost() {
        return 0;
    }
    
    public final boolean isOverdue() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.data.entity.Employee component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.data.entity.EmployeeWithStats copy(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee, int daysSinceLastPost, boolean isOverdue) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/xue/hongshu/data/entity/EmployeeWithStats$Companion;", "", "()V", "OVERDUE_DAYS", "", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}