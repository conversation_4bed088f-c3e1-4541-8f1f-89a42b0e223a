package com.xue.hongshu.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0010\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\fH\'J\u0018\u0010\u000e\u001a\u0004\u0018\u00010\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0012\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\u0013\u001a\u00020\u0014H\'J\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\u0018\u001a\u00020\u0019H\'J\u0016\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u001b\u001a\u00020\u00032\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00050\rH\u00a7@\u00a2\u0006\u0002\u0010\u001dJ\u0016\u0010\u001e\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J.\u0010\u001f\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010 \u001a\u00020\t2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010!\u001a\u00020\u0019H\u00a7@\u00a2\u0006\u0002\u0010\"J6\u0010#\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010$\u001a\u00020\u00192\u0006\u0010%\u001a\u00020\t2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010!\u001a\u00020\u0019H\u00a7@\u00a2\u0006\u0002\u0010&J&\u0010\'\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010!\u001a\u00020\u0019H\u00a7@\u00a2\u0006\u0002\u0010(\u00a8\u0006)"}, d2 = {"Lcom/xue/hongshu/data/dao/EmployeeDao;", "", "deleteEmployee", "", "employee", "Lcom/xue/hongshu/data/entity/Employee;", "(Lcom/xue/hongshu/data/entity/Employee;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteEmployeeById", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllEmployees", "Lkotlinx/coroutines/flow/Flow;", "", "getEmployeeById", "getEmployeeCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getEmployeeCountByStatus", "status", "Lcom/xue/hongshu/data/entity/EmployeeStatus;", "(Lcom/xue/hongshu/data/entity/EmployeeStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getEmployeesByStatus", "getOverdueEmployees", "threshold", "", "insertEmployee", "insertEmployees", "employees", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateEmployee", "updateEmployeeError", "errorMessage", "checkTime", "(Ljava/lang/String;Ljava/lang/String;Lcom/xue/hongshu/data/entity/EmployeeStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateEmployeePostInfo", "postTime", "title", "(Ljava/lang/String;JLjava/lang/String;Lcom/xue/hongshu/data/entity/EmployeeStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateEmployeeStatus", "(Ljava/lang/String;Lcom/xue/hongshu/data/entity/EmployeeStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface EmployeeDao {
    
    @androidx.room.Query(value = "SELECT * FROM employees ORDER BY lastPostTime DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.xue.hongshu.data.entity.Employee>> getAllEmployees();
    
    @androidx.room.Query(value = "SELECT * FROM employees WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmployeeById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.xue.hongshu.data.entity.Employee> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM employees WHERE status = :status ORDER BY lastPostTime DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.xue.hongshu.data.entity.Employee>> getEmployeesByStatus(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeStatus status);
    
    @androidx.room.Query(value = "SELECT * FROM employees WHERE lastPostTime < :threshold ORDER BY lastPostTime ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.xue.hongshu.data.entity.Employee>> getOverdueEmployees(long threshold);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM employees")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmployeeCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM employees WHERE status = :status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmployeeCountByStatus(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertEmployee(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertEmployees(@org.jetbrains.annotations.NotNull()
    java.util.List<com.xue.hongshu.data.entity.Employee> employees, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateEmployee(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteEmployee(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM employees WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteEmployeeById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE employees SET status = :status, lastCheckTime = :checkTime WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateEmployeeStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeStatus status, long checkTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE employees SET lastPostTime = :postTime, lastPostTitle = :title, status = :status, lastCheckTime = :checkTime, checkCount = checkCount + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateEmployeePostInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String id, long postTime, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeStatus status, long checkTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE employees SET errorMessage = :errorMessage, status = :status, lastCheckTime = :checkTime WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateEmployeeError(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeStatus status, long checkTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}