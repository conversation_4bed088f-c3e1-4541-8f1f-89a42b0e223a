package com.xue.hongshu.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0014\b\u0007\u0018\u0000 (2\u00020\u0001:\u0003()*B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0002J\u0006\u0010\u000b\u001a\u00020\fJ\u0006\u0010\r\u001a\u00020\u000eJ\u0006\u0010\u000f\u001a\u00020\fJ\u0006\u0010\u0010\u001a\u00020\u0011J\u0006\u0010\u0012\u001a\u00020\u000eJ\u0006\u0010\u0013\u001a\u00020\u0014J\u0006\u0010\u0015\u001a\u00020\u000eJ\u0006\u0010\u0016\u001a\u00020\u0017J\u0006\u0010\u0018\u001a\u00020\u000eJ\u0006\u0010\u0019\u001a\u00020\bJ\u0006\u0010\u001a\u001a\u00020\bJ\u0006\u0010\u001b\u001a\u00020\bJ\u000e\u0010\u001c\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u000eJ\u000e\u0010\u001e\u001a\u00020\b2\u0006\u0010\u001f\u001a\u00020\fJ\u000e\u0010 \u001a\u00020\b2\u0006\u0010!\u001a\u00020\u0014J\u000e\u0010\"\u001a\u00020\b2\u0006\u0010#\u001a\u00020\u000eJ\u000e\u0010$\u001a\u00020\b2\u0006\u0010%\u001a\u00020\u0017J\u0006\u0010&\u001a\u00020\nJ\u0006\u0010\'\u001a\u00020\nR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006+"}, d2 = {"Lcom/xue/hongshu/utils/ConfigManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "prefs", "Landroid/content/SharedPreferences;", "adjustDetectionLevel", "", "isFailure", "", "getAdaptiveDelay", "", "getAdaptiveRetryCount", "", "getBaseDelay", "getConfigSummary", "", "getConsecutiveFailures", "getDetectionLevel", "Lcom/xue/hongshu/utils/ConfigManager$DetectionLevel;", "getRetryCount", "getStrategyMode", "Lcom/xue/hongshu/utils/ConfigManager$StrategyMode;", "getUserAgentIndex", "recordFailure", "recordSuccess", "resetConfig", "rotateUserAgent", "maxIndex", "setBaseDelay", "delay", "setDetectionLevel", "level", "setRetryCount", "count", "setStrategyMode", "mode", "shouldSwitchStrategy", "shouldUseAdvancedAntiDetection", "Companion", "DetectionLevel", "StrategyMode", "app_debug"})
public final class ConfigManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "hongshu_config";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_DETECTION_LEVEL = "detection_level";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_RETRY_COUNT = "retry_count";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_BASE_DELAY = "base_delay";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_USER_AGENT_INDEX = "user_agent_index";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_LAST_SUCCESS_TIME = "last_success_time";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_CONSECUTIVE_FAILURES = "consecutive_failures";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_STRATEGY_MODE = "strategy_mode";
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences prefs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.xue.hongshu.utils.ConfigManager.Companion Companion = null;
    
    public ConfigManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.utils.ConfigManager.DetectionLevel getDetectionLevel() {
        return null;
    }
    
    public final void setDetectionLevel(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.ConfigManager.DetectionLevel level) {
    }
    
    public final int getRetryCount() {
        return 0;
    }
    
    public final void setRetryCount(int count) {
    }
    
    public final long getBaseDelay() {
        return 0L;
    }
    
    public final void setBaseDelay(long delay) {
    }
    
    public final int getUserAgentIndex() {
        return 0;
    }
    
    public final void rotateUserAgent(int maxIndex) {
    }
    
    public final void recordSuccess() {
    }
    
    public final void recordFailure() {
    }
    
    public final int getConsecutiveFailures() {
        return 0;
    }
    
    private final void adjustDetectionLevel(boolean isFailure) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.utils.ConfigManager.StrategyMode getStrategyMode() {
        return null;
    }
    
    public final void setStrategyMode(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.ConfigManager.StrategyMode mode) {
    }
    
    public final long getAdaptiveDelay() {
        return 0L;
    }
    
    public final int getAdaptiveRetryCount() {
        return 0;
    }
    
    public final boolean shouldUseAdvancedAntiDetection() {
        return false;
    }
    
    public final boolean shouldSwitchStrategy() {
        return false;
    }
    
    public final void resetConfig() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConfigSummary() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/xue/hongshu/utils/ConfigManager$Companion;", "", "()V", "KEY_BASE_DELAY", "", "KEY_CONSECUTIVE_FAILURES", "KEY_DETECTION_LEVEL", "KEY_LAST_SUCCESS_TIME", "KEY_RETRY_COUNT", "KEY_STRATEGY_MODE", "KEY_USER_AGENT_INDEX", "PREFS_NAME", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/xue/hongshu/utils/ConfigManager$DetectionLevel;", "", "(Ljava/lang/String;I)V", "LOW", "MEDIUM", "HIGH", "app_debug"})
    public static enum DetectionLevel {
        /*public static final*/ LOW /* = new LOW() */,
        /*public static final*/ MEDIUM /* = new MEDIUM() */,
        /*public static final*/ HIGH /* = new HIGH() */;
        
        DetectionLevel() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.xue.hongshu.utils.ConfigManager.DetectionLevel> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/xue/hongshu/utils/ConfigManager$StrategyMode;", "", "(Ljava/lang/String;I)V", "NORMAL", "STEALTH", "AGGRESSIVE", "app_debug"})
    public static enum StrategyMode {
        /*public static final*/ NORMAL /* = new NORMAL() */,
        /*public static final*/ STEALTH /* = new STEALTH() */,
        /*public static final*/ AGGRESSIVE /* = new AGGRESSIVE() */;
        
        StrategyMode() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.xue.hongshu.utils.ConfigManager.StrategyMode> getEntries() {
            return null;
        }
    }
}