package com.xue.hongshu.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * SSL配置管理器
 * 管理SSL相关的配置和策略
 */
class SslConfigManager(context: Context) {
    
    companion object {
        private const val TAG = "SslConfigManager"
        private const val PREFS_NAME = "ssl_config"
        
        // 配置键
        private const val KEY_SSL_VERIFICATION_ENABLED = "ssl_verification_enabled"
        private const val KEY_CERTIFICATE_PINNING_ENABLED = "certificate_pinning_enabled"
        private const val KEY_ALLOW_SELF_SIGNED = "allow_self_signed"
        private const val KEY_MIN_TLS_VERSION = "min_tls_version"
        private const val KEY_SSL_ERROR_COUNT = "ssl_error_count"
        private const val KEY_LAST_SSL_ERROR_TIME = "last_ssl_error_time"
        private const val KEY_SSL_BYPASS_DOMAINS = "ssl_bypass_domains"
        
        // 默认值
        private const val DEFAULT_MIN_TLS_VERSION = "TLSv1.2"
        private const val DEFAULT_SSL_BYPASS_DOMAINS = "xiaohongshu.com,xhscdn.com"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * TLS版本枚举
     */
    enum class TlsVersion(val version: String) {
        TLS_1_0("TLSv1"),
        TLS_1_1("TLSv1.1"),
        TLS_1_2("TLSv1.2"),
        TLS_1_3("TLSv1.3")
    }
    
    /**
     * SSL配置数据类
     */
    data class SslConfig(
        val sslVerificationEnabled: Boolean = true,
        val certificatePinningEnabled: Boolean = false,
        val allowSelfSigned: Boolean = false,
        val minTlsVersion: TlsVersion = TlsVersion.TLS_1_2,
        val bypassDomains: Set<String> = emptySet()
    )
    
    /**
     * 获取当前SSL配置
     */
    fun getSslConfig(): SslConfig {
        val bypassDomainsString = prefs.getString(KEY_SSL_BYPASS_DOMAINS, DEFAULT_SSL_BYPASS_DOMAINS) ?: ""
        val bypassDomains = bypassDomainsString.split(",").map { it.trim() }.filter { it.isNotEmpty() }.toSet()
        
        val minTlsVersionString = prefs.getString(KEY_MIN_TLS_VERSION, DEFAULT_MIN_TLS_VERSION) ?: DEFAULT_MIN_TLS_VERSION
        val minTlsVersion = TlsVersion.values().find { it.version == minTlsVersionString } ?: TlsVersion.TLS_1_2
        
        return SslConfig(
            sslVerificationEnabled = prefs.getBoolean(KEY_SSL_VERIFICATION_ENABLED, true),
            certificatePinningEnabled = prefs.getBoolean(KEY_CERTIFICATE_PINNING_ENABLED, false),
            allowSelfSigned = prefs.getBoolean(KEY_ALLOW_SELF_SIGNED, false),
            minTlsVersion = minTlsVersion,
            bypassDomains = bypassDomains
        )
    }
    
    /**
     * 更新SSL配置
     */
    fun updateSslConfig(config: SslConfig) {
        prefs.edit().apply {
            putBoolean(KEY_SSL_VERIFICATION_ENABLED, config.sslVerificationEnabled)
            putBoolean(KEY_CERTIFICATE_PINNING_ENABLED, config.certificatePinningEnabled)
            putBoolean(KEY_ALLOW_SELF_SIGNED, config.allowSelfSigned)
            putString(KEY_MIN_TLS_VERSION, config.minTlsVersion.version)
            putString(KEY_SSL_BYPASS_DOMAINS, config.bypassDomains.joinToString(","))
            apply()
        }
        
        Log.i(TAG, "SSL配置已更新: $config")
    }
    
    /**
     * 检查域名是否在SSL绕过列表中
     */
    fun isDomainInBypassList(domain: String): Boolean {
        val config = getSslConfig()
        return config.bypassDomains.any { bypassDomain ->
            domain.contains(bypassDomain, ignoreCase = true)
        }
    }
    
    /**
     * 记录SSL错误
     */
    fun recordSslError() {
        val currentCount = prefs.getInt(KEY_SSL_ERROR_COUNT, 0)
        val currentTime = System.currentTimeMillis()
        
        prefs.edit().apply {
            putInt(KEY_SSL_ERROR_COUNT, currentCount + 1)
            putLong(KEY_LAST_SSL_ERROR_TIME, currentTime)
            apply()
        }
        
        Log.w(TAG, "SSL错误计数: ${currentCount + 1}")
        
        // 如果错误过多，可以考虑调整配置
        if (currentCount + 1 >= 5) {
            Log.w(TAG, "SSL错误过多，考虑调整配置")
            suggestConfigAdjustment()
        }
    }
    
    /**
     * 获取SSL错误统计
     */
    fun getSslErrorStats(): Pair<Int, Long> {
        val errorCount = prefs.getInt(KEY_SSL_ERROR_COUNT, 0)
        val lastErrorTime = prefs.getLong(KEY_LAST_SSL_ERROR_TIME, 0)
        return Pair(errorCount, lastErrorTime)
    }
    
    /**
     * 重置SSL错误统计
     */
    fun resetSslErrorStats() {
        prefs.edit().apply {
            putInt(KEY_SSL_ERROR_COUNT, 0)
            putLong(KEY_LAST_SSL_ERROR_TIME, 0)
            apply()
        }
        Log.i(TAG, "SSL错误统计已重置")
    }
    
    /**
     * 建议配置调整
     */
    private fun suggestConfigAdjustment() {
        val config = getSslConfig()
        
        val suggestions = mutableListOf<String>()
        
        if (config.minTlsVersion == TlsVersion.TLS_1_3) {
            suggestions.add("考虑降低最低TLS版本到1.2以提高兼容性")
        }
        
        if (config.certificatePinningEnabled) {
            suggestions.add("考虑暂时禁用证书固定以排查问题")
        }
        
        if (!config.allowSelfSigned) {
            suggestions.add("如果使用测试环境，考虑允许自签名证书")
        }
        
        Log.i(TAG, "SSL配置调整建议: ${suggestions.joinToString("; ")}")
    }
    
    /**
     * 获取推荐的SSL配置（基于环境）
     */
    fun getRecommendedConfig(isDebugMode: Boolean): SslConfig {
        return if (isDebugMode) {
            // 调试模式：较宽松的配置
            SslConfig(
                sslVerificationEnabled = true,
                certificatePinningEnabled = false,
                allowSelfSigned = true,
                minTlsVersion = TlsVersion.TLS_1_2,
                bypassDomains = setOf("xiaohongshu.com", "xhscdn.com", "localhost", "127.0.0.1")
            )
        } else {
            // 生产模式：严格的配置
            SslConfig(
                sslVerificationEnabled = true,
                certificatePinningEnabled = true,
                allowSelfSigned = false,
                minTlsVersion = TlsVersion.TLS_1_2,
                bypassDomains = setOf("xiaohongshu.com", "xhscdn.com")
            )
        }
    }
    
    /**
     * 应用推荐配置
     */
    fun applyRecommendedConfig(isDebugMode: Boolean) {
        val recommendedConfig = getRecommendedConfig(isDebugMode)
        updateSslConfig(recommendedConfig)
        Log.i(TAG, "已应用推荐的SSL配置 (调试模式: $isDebugMode)")
    }
    
    /**
     * 验证SSL配置的合理性
     */
    fun validateSslConfig(config: SslConfig): List<String> {
        val warnings = mutableListOf<String>()
        
        if (!config.sslVerificationEnabled) {
            warnings.add("SSL验证已禁用，存在安全风险")
        }
        
        if (config.allowSelfSigned) {
            warnings.add("允许自签名证书，仅适用于开发环境")
        }
        
        if (config.minTlsVersion == TlsVersion.TLS_1_0 || config.minTlsVersion == TlsVersion.TLS_1_1) {
            warnings.add("使用较旧的TLS版本，建议升级到TLS 1.2或更高")
        }
        
        if (config.bypassDomains.isEmpty()) {
            warnings.add("没有配置SSL绕过域名，可能影响某些功能")
        }
        
        return warnings
    }
    
    /**
     * 导出SSL配置（用于备份或分享）
     */
    fun exportConfig(): String {
        val config = getSslConfig()
        return """
            SSL_VERIFICATION_ENABLED=${config.sslVerificationEnabled}
            CERTIFICATE_PINNING_ENABLED=${config.certificatePinningEnabled}
            ALLOW_SELF_SIGNED=${config.allowSelfSigned}
            MIN_TLS_VERSION=${config.minTlsVersion.version}
            BYPASS_DOMAINS=${config.bypassDomains.joinToString(",")}
        """.trimIndent()
    }
    
    /**
     * 导入SSL配置
     */
    fun importConfig(configString: String): Boolean {
        return try {
            val lines = configString.split("\n")
            val configMap = lines.associate { line ->
                val parts = line.split("=", limit = 2)
                if (parts.size == 2) parts[0].trim() to parts[1].trim() else "" to ""
            }
            
            val sslVerificationEnabled = configMap["SSL_VERIFICATION_ENABLED"]?.toBoolean() ?: true
            val certificatePinningEnabled = configMap["CERTIFICATE_PINNING_ENABLED"]?.toBoolean() ?: false
            val allowSelfSigned = configMap["ALLOW_SELF_SIGNED"]?.toBoolean() ?: false
            val minTlsVersionString = configMap["MIN_TLS_VERSION"] ?: DEFAULT_MIN_TLS_VERSION
            val minTlsVersion = TlsVersion.values().find { it.version == minTlsVersionString } ?: TlsVersion.TLS_1_2
            val bypassDomainsString = configMap["BYPASS_DOMAINS"] ?: ""
            val bypassDomains = bypassDomainsString.split(",").map { it.trim() }.filter { it.isNotEmpty() }.toSet()
            
            val importedConfig = SslConfig(
                sslVerificationEnabled = sslVerificationEnabled,
                certificatePinningEnabled = certificatePinningEnabled,
                allowSelfSigned = allowSelfSigned,
                minTlsVersion = minTlsVersion,
                bypassDomains = bypassDomains
            )
            
            updateSslConfig(importedConfig)
            Log.i(TAG, "SSL配置导入成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "SSL配置导入失败", e)
            false
        }
    }
}
