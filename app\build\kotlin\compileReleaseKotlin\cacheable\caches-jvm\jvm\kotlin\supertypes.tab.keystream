com.xue.hongshu.MainActivity(com.xue.hongshu.activity.WebViewActivity)com.xue.hongshu.data.database.AppDatabase*com.xue.hongshu.data.entity.EmployeeStatus<com.xue.hongshu.network.SecureHttpClient.SslErrorInterceptorFcom.xue.hongshu.utils.CertificateHelper.CertificateCaptureTrustManager2com.xue.hongshu.utils.ConfigManager.DetectionLevel0com.xue.hongshu.utils.ConfigManager.StrategyMode,com.xue.hongshu.utils.ErrorHandler.ErrorType"com.xue.hongshu.utils.NetworkState'com.xue.hongshu.utils.ConnectionQuality1com.xue.hongshu.utils.SslConfigManager.TlsVersion>com.xue.hongshu.utils.SslDiagnosticTool.DiagnosticTrustManagerBcom.xue.hongshu.utils.SslDiagnosticTool.DiagnosticHostnameVerifier'com.xue.hongshu.viewmodel.MainViewModel.com.xue.hongshu.viewmodel.MainViewModelFactory(com.xue.hongshu.webview.XhsWebViewClient                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 