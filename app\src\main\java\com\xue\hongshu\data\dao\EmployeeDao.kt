package com.xue.hongshu.data.dao

import androidx.room.*
import com.xue.hongshu.data.entity.Employee
import com.xue.hongshu.data.entity.EmployeeStatus
import kotlinx.coroutines.flow.Flow

@Dao
interface EmployeeDao {
    
    @Query("SELECT * FROM employees ORDER BY lastPostTime DESC")
    fun getAllEmployees(): Flow<List<Employee>>
    
    @Query("SELECT * FROM employees WHERE id = :id")
    suspend fun getEmployeeById(id: String): Employee?
    
    @Query("SELECT * FROM employees WHERE status = :status ORDER BY lastPostTime DESC")
    fun getEmployeesByStatus(status: EmployeeStatus): Flow<List<Employee>>
    
    @Query("SELECT * FROM employees WHERE lastPostTime < :threshold ORDER BY lastPostTime ASC")
    fun getOverdueEmployees(threshold: Long): Flow<List<Employee>>
    
    @Query("SELECT COUNT(*) FROM employees")
    suspend fun getEmployeeCount(): Int
    
    @Query("SELECT COUNT(*) FROM employees WHERE status = :status")
    suspend fun getEmployeeCountByStatus(status: EmployeeStatus): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmployee(employee: Employee)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmployees(employees: List<Employee>)
    
    @Update
    suspend fun updateEmployee(employee: Employee)
    
    @Delete
    suspend fun deleteEmployee(employee: Employee)
    
    @Query("DELETE FROM employees WHERE id = :id")
    suspend fun deleteEmployeeById(id: String)
    
    @Query("UPDATE employees SET status = :status, lastCheckTime = :checkTime WHERE id = :id")
    suspend fun updateEmployeeStatus(id: String, status: EmployeeStatus, checkTime: Long)
    
    @Query("UPDATE employees SET lastPostTime = :postTime, lastPostTitle = :title, status = :status, lastCheckTime = :checkTime, checkCount = checkCount + 1 WHERE id = :id")
    suspend fun updateEmployeePostInfo(
        id: String, 
        postTime: Long, 
        title: String, 
        status: EmployeeStatus, 
        checkTime: Long
    )
    
    @Query("UPDATE employees SET errorMessage = :errorMessage, status = :status, lastCheckTime = :checkTime WHERE id = :id")
    suspend fun updateEmployeeError(id: String, errorMessage: String, status: EmployeeStatus, checkTime: Long)
}
