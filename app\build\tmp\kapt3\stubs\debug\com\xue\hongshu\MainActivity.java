package com.xue.hongshu;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\nH\u0014J\u0010\u0010\u000b\u001a\u00020\b2\u0006\u0010\f\u001a\u00020\rH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/xue/hongshu/MainActivity;", "Landroidx/activity/ComponentActivity;", "()V", "database", "Lcom/xue/hongshu/data/database/AppDatabase;", "repository", "Lcom/xue/hongshu/repository/EmployeeRepository;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "startWebViewActivity", "employeeId", "", "app_debug"})
public final class MainActivity extends androidx.activity.ComponentActivity {
    private com.xue.hongshu.data.database.AppDatabase database;
    private com.xue.hongshu.repository.EmployeeRepository repository;
    
    public MainActivity() {
        super(0);
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void startWebViewActivity(java.lang.String employeeId) {
    }
}