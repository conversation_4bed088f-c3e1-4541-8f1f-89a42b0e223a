--------- beginning of main
2025-08-08 00:00:38.690  4029-4029  UserAction              com.xue.hongshu                      I  用户操作: 启动WebView检查 - 员工ID: fb69f122-84a9-43ba-93f6-6de78f6c2fb8
--------- beginning of system
2025-08-08 00:00:38.696  4029-4029  UserAction              com.xue.hongshu                      I  用户操作: 启动WebView - 员工: 薛博文, 小红书ID: 64d1afd0000000000b007f59
2025-08-08 00:00:38.728  4029-4029  ActivityThread          com.xue.hongshu                      W  handleWindowVisibility: no activity for token android.os.BinderProxy@ab7be9e
2025-08-08 00:00:38.801  4029-4029  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/view/RenderNode;->getScaleX()F (dark greylist, linking)
2025-08-08 00:00:38.951  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:38.954  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:39.004  4029-4058  EGL_emulation           com.xue.hongshu                      E  tid 4058: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:00:39.004  4029-4058  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763875b98180, error=EGL_BAD_MATCH
2025-08-08 00:00:39.030  4029-4058  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:39.033  4029-4058  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000087fe
2025-08-08 00:00:39.034  4029-4058  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:39.034  4029-4058  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:39.095  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:39.098  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(73)] "Enhanced anti-detection script loaded", source:  (73)
2025-08-08 00:00:39.099  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(30)] "Bypass detection script loaded", source:  (30)
2025-08-08 00:00:39.410  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Page started loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:00:39.436  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(0)] "Mixed Content: The page at 'https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59' was loaded over HTTPS, but requested an insecure image 'http://sns-avatar-qc.xhscdn.com/user_banner/1040g2k031i1bh3qin0dg5p6hlv82ovqp82bj5eo?imageView2/2/w/540/format/jpg'. This content should also be served over HTTPS.", source: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59 (0)
2025-08-08 00:00:39.609  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "框架和 SDK 输出的日志默认不展示，可在框架配置文件中设置开启，详见 https://doc.weixin.qq.com/doc/w3_AWkASAb9APAr8IdJI5VS0OyqetUE6?scode=ANAAyQcbAAgB8qKjm9AWkASAb9APA", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:39.816  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-08 00:00:39.889  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:39.907  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 2 lines
2025-08-08 00:00:39.916  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:39.919  4029-4029  chromium                com.xue.hongshu                      E  [ERROR:web_contents_delegate.cc(225)] WebContentsDelegate::CheckMediaAccessPermission: Not supported.
2025-08-08 00:00:39.919  4029-4029  chromium                com.xue.hongshu                      E  [ERROR:web_contents_delegate.cc(225)] WebContentsDelegate::CheckMediaAccessPermission: Not supported.
2025-08-08 00:00:39.932  4029-4094  cr_media                com.xue.hongshu                      W  Requires MODIFY_AUDIO_SETTINGS and RECORD_AUDIO. No audio device will be available for recording
2025-08-08 00:00:39.969  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:00:39.969  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Waiting 4728ms before extracting data
2025-08-08 00:00:40.272  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:00:40.272  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Waiting 3201ms before extracting data
2025-08-08 00:00:40.578  4029-4058  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:00:40.619  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.120  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 7 lines
2025-08-08 00:00:41.125  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.155  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-08 00:00:41.155  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Waiting 4991ms before extracting data
2025-08-08 00:00:41.187  4029-4094  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:00:41.187  4029-4094  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:00:41.187  4029-4094  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:00:41.188  4029-4094  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:00:41.188  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.209  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.215  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.220  4029-4104  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:00:41.221  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.264  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 2 lines
2025-08-08 00:00:41.267  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.269  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:00:41.271  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:00:41.272  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.284  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:00:41.288  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.300  4029-4094  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:00:41.301  4029-4094  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:00:41.301  4029-4094  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:00:41.301  4029-4094  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:00:41.302  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.345  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 5 lines
2025-08-08 00:00:41.350  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.352  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:00:41.355  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:00:41.356  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.368  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:00:41.372  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.381  4029-4094  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:00:41.381  4029-4094  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:00:41.381  4029-4094  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:00:41.382  4029-4094  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:00:41.383  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.393  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 1 line
2025-08-08 00:00:41.395  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.399  4029-4104  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:00:41.401  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.427  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 2 lines
2025-08-08 00:00:41.429  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.431  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:00:41.433  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:00:41.434  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.447  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:00:41.450  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.458  4029-4094  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-08 00:00:41.458  4029-4094  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-08 00:00:41.458  4029-4094  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-08 00:00:41.458  4029-4094  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-08 00:00:41.460  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.496  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 5 lines
2025-08-08 00:00:41.499  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.501  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:00:41.502  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-08 00:00:41.503  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.513  4029-4104  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-08 00:00:41.516  4029-4104  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] pageView", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] visitMobile", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] AbTestPluginMetrics", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] AbTestPluginMetrics", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.627  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.628  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.687  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.688  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.697  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.697  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.697  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.697  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.698  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.699  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.699  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.699  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:41.699  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:42.141  4029-4104  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-08 00:00:43.473  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:00:43.473  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:00:44.562  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.562  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.562  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.563  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.564  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.564  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.564  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.564  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.564  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.564  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.564  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.564  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserMemory", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserNetwork", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserNavigationTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserScriptsExecutionTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserRenderTimes", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.566  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.571  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:00:44.571  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:00:44.571  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:00:44.571  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:00:44.571  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (48)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .user-name found 1 elements", source:  (52)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(57)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (57)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(68)] "Starting enhanced time search...", source:  (68)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (48)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .time-text found 0 elements", source:  (52)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .publish-time found 0 elements", source:  (52)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-time found 0 elements", source:  (52)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .create-time found 0 elements", source:  (52)
2025-08-08 00:00:44.572  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [data-time] found 0 elements", source:  (52)
2025-08-08 00:00:44.573  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-time found 0 elements", source:  (52)
2025-08-08 00:00:44.573  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .timestamp found 0 elements", source:  (52)
2025-08-08 00:00:44.573  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector time found 0 elements", source:  (52)
2025-08-08 00:00:44.573  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .date found 0 elements", source:  (52)
2025-08-08 00:00:44.573  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .publish-date found 0 elements", source:  (52)
2025-08-08 00:00:44.573  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .creation-time found 0 elements", source:  (52)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-date found 0 elements", source:  (52)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="time"] found 0 elements", source:  (52)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="date"] found 0 elements", source:  (52)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="publish"] found 0 elements", source:  (52)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-item time found 0 elements", source:  (52)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .feed-item time found 0 elements", source:  (52)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-item time found 0 elements", source:  (52)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(62)] "No time found", source:  (62)
2025-08-08 00:00:44.574  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(75)] "Searching for time patterns in text content...", source:  (75)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(109)] "No time element found with text patterns", source:  (109)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(115)] "Searching for latest note time...", source:  (115)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for title with selectors: .note-title,.post-title,.title,.content-title,h1,h2,h3,.note-content,.post-content", source:  (48)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-title found 0 elements", source:  (52)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-title found 0 elements", source:  (52)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .title found 0 elements", source:  (52)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .content-title found 0 elements", source:  (52)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h1 found 0 elements", source:  (52)
2025-08-08 00:00:44.576  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h2 found 0 elements", source:  (52)
2025-08-08 00:00:44.577  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h3 found 0 elements", source:  (52)
2025-08-08 00:00:44.577  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-content found 0 elements", source:  (52)
2025-08-08 00:00:44.577  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-content found 0 elements", source:  (52)
2025-08-08 00:00:44.577  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(62)] "No title found", source:  (62)
2025-08-08 00:00:44.577  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(154)] "Page analysis: [object Object]", source:  (154)
2025-08-08 00:00:44.577  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "Extraction result: [object Object]", source:  (192)
2025-08-08 00:00:44.577  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:00:44.577  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":true,\"userName\":\"哈尔滨金包银老刘\",\"lastPostTime\":\"\",\"lastPostTitle\":\"\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754582444577,\"debug\":{\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"title\":\"@哈尔滨金包银老刘 的个人主页\",\"bodyClasses\":\"\",\"allElements\":216,\"hasUserInfo\":true,\"hasTimeInfo\":false,\"hasTitleInfo\":false,\"userNameSelector\":\"DIV.user-name\",\"timeSelector\":\"none\",\"titleSelector\":\"none\",\"timeElementText\":\"none\",\"timeElementAttributes\":[]}}"
2025-08-08 00:00:44.579  4029-4029  DataExtraction          com.xue.hongshu                      I  数据抓取: 员工ID=fb69f122-84a9-43ba-93f6-6de78f6c2fb8, 结果=成功, 详情=用户名: 哈尔滨金包银老刘, 时间: 
2025-08-08 00:00:44.582  4029-4029  System.err              com.xue.hongshu                      W  java.lang.Exception: Toast callstack! strTip=数据更新成功
2025-08-08 00:00:44.582  4029-4029  System.err              com.xue.hongshu                      W  用户名: 哈尔滨金包银老刘
2025-08-08 00:00:44.582  4029-4029  System.err              com.xue.hongshu                      W  最新发布: 未获取到时间信息
2025-08-08 00:00:44.582  4029-4029  System.err              com.xue.hongshu                      W  	at android.widget.Toast.show(Toast.java:143)
2025-08-08 00:00:44.582  4029-4029  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.invokeSuspend$lambda$0(WebViewActivity.kt:95)
2025-08-08 00:00:44.582  4029-4029  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.$r8$lambda$OaNj8h7GSrZfJk-HuBtkTZRZUMw(Unknown Source:0)
2025-08-08 00:00:44.582  4029-4029  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at android.app.Activity.runOnUiThread(Activity.java:6423)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleDataExtracted$1.invokeSuspend(WebViewActivity.kt:93)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at android.os.Handler.handleCallback(Handler.java:873)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at android.os.Handler.dispatchMessage(Handler.java:99)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at android.os.Looper.loop(Looper.java:193)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at android.app.ActivityThread.main(ActivityThread.java:6834)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at java.lang.reflect.Method.invoke(Native Method)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
2025-08-08 00:00:44.583  4029-4029  System.err              com.xue.hongshu                      W  	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-08-08 00:00:44.631  4029-4058  EGL_emulation           com.xue.hongshu                      E  tid 4058: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:00:44.631  4029-4058  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878c72200, error=EGL_BAD_MATCH
2025-08-08 00:00:44.674  4029-4058  EGL_emulation           com.xue.hongshu                      E  tid 4058: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-08 00:00:44.674  4029-4058  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878c25580, error=EGL_BAD_MATCH
2025-08-08 00:00:44.708  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:00:44.708  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://dhjaibcmgocbpegmejfphjhhpaphmkpp/is.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://beeaddblkeeialcohiolkkoiifhgjooj/manifest.json. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://ddgooekaaihgnbfbgalfiooiicdnmnia/js/js-index.ts.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://keeelahekhhgkpaipdodgjnmgkfcdpde/inject.6070000c.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://dbichmdlbjdeplpkhcejgkakobjbjalc/content-scripts/xiaohongshu.css. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://piejlhcmefdepbgalcongckfomfobokb/worker.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://lenndnnbdichpjlfmfadcjpaenmiflan/inject.bundle.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://hmeohemhimcjlegdjloglnkfablbneif/inject.bundle.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (48)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .user-name found 1 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(57)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (57)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(68)] "Starting enhanced time search...", source:  (68)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (48)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .time-text found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .publish-time found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-time found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .create-time found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [data-time] found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-time found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .timestamp found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector time found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .date found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .publish-date found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .creation-time found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-date found 0 elements", source:  (52)
2025-08-08 00:00:44.833  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="time"] found 0 elements", source:  (52)
2025-08-08 00:00:44.834  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="date"] found 0 elements", source:  (52)
2025-08-08 00:00:44.834  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="publish"] found 0 elements", source:  (52)
2025-08-08 00:00:44.834  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-item time found 0 elements", source:  (52)
2025-08-08 00:00:44.834  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .feed-item time found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-item time found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(62)] "No time found", source:  (62)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(75)] "Searching for time patterns in text content...", source:  (75)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(109)] "No time element found with text patterns", source:  (109)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(115)] "Searching for latest note time...", source:  (115)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for title with selectors: .note-title,.post-title,.title,.content-title,h1,h2,h3,.note-content,.post-content", source:  (48)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-title found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-title found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .title found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .content-title found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h1 found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h2 found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h3 found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-content found 0 elements", source:  (52)
2025-08-08 00:00:44.859  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-content found 0 elements", source:  (52)
2025-08-08 00:00:44.860  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(62)] "No title found", source:  (62)
2025-08-08 00:00:44.860  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(154)] "Page analysis: [object Object]", source:  (154)
2025-08-08 00:00:44.860  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "Extraction result: [object Object]", source:  (192)
2025-08-08 00:00:44.860  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm performance metric] paintTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.860  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.860  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm performance metric] visualStability", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.860  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:44.863  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Ignoring debug-related error: net::ERR_CONNECTION_REFUSED for http://127.0.0.1:9222/json/version
2025-08-08 00:00:44.863  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Ignoring debug-related error: net::ERR_CONNECTION_REFUSED for http://127.0.0.1:54345/
2025-08-08 00:00:44.863  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:00:44.863  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":true,\"userName\":\"哈尔滨金包银老刘\",\"lastPostTime\":\"\",\"lastPostTitle\":\"\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754582444835,\"debug\":{\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"title\":\"@哈尔滨金包银老刘 的个人主页\",\"bodyClasses\":\"\",\"allElements\":216,\"hasUserInfo\":true,\"hasTimeInfo\":false,\"hasTitleInfo\":false,\"userNameSelector\":\"DIV.user-name\",\"timeSelector\":\"none\",\"titleSelector\":\"none\",\"timeElementText\":\"none\",\"timeElementAttributes\":[]}}"
2025-08-08 00:00:46.147  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-08 00:00:46.147  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-08 00:00:46.147  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (48)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .user-name found 1 elements", source:  (52)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(57)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (57)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(68)] "Starting enhanced time search...", source:  (68)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time", source:  (48)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .time-text found 0 elements", source:  (52)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .publish-time found 0 elements", source:  (52)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-time found 0 elements", source:  (52)
2025-08-08 00:00:46.148  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .create-time found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [data-time] found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-time found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .timestamp found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector time found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .date found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .publish-date found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .creation-time found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-date found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="time"] found 0 elements", source:  (52)
2025-08-08 00:00:46.149  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="date"] found 0 elements", source:  (52)
2025-08-08 00:00:46.150  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector [class*="publish"] found 0 elements", source:  (52)
2025-08-08 00:00:46.150  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-item time found 0 elements", source:  (52)
2025-08-08 00:00:46.150  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .feed-item time found 0 elements", source:  (52)
2025-08-08 00:00:46.150  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-item time found 0 elements", source:  (52)
2025-08-08 00:00:46.150  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(62)] "No time found", source:  (62)
2025-08-08 00:00:46.150  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(75)] "Searching for time patterns in text content...", source:  (75)
2025-08-08 00:00:46.151  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(109)] "No time element found with text patterns", source:  (109)
2025-08-08 00:00:46.151  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(115)] "Searching for latest note time...", source:  (115)
2025-08-08 00:00:46.152  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(48)] "Searching for title with selectors: .note-title,.post-title,.title,.content-title,h1,h2,h3,.note-content,.post-content", source:  (48)
2025-08-08 00:00:46.152  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-title found 0 elements", source:  (52)
2025-08-08 00:00:46.152  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-title found 0 elements", source:  (52)
2025-08-08 00:00:46.152  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .title found 0 elements", source:  (52)
2025-08-08 00:00:46.152  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .content-title found 0 elements", source:  (52)
2025-08-08 00:00:46.152  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h1 found 0 elements", source:  (52)
2025-08-08 00:00:46.152  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h2 found 0 elements", source:  (52)
2025-08-08 00:00:46.152  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector h3 found 0 elements", source:  (52)
2025-08-08 00:00:46.153  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .note-content found 0 elements", source:  (52)
2025-08-08 00:00:46.153  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(52)] "Selector .post-content found 0 elements", source:  (52)
2025-08-08 00:00:46.153  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(62)] "No title found", source:  (62)
2025-08-08 00:00:46.153  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(154)] "Page analysis: [object Object]", source:  (154)
2025-08-08 00:00:46.153  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(192)] "Extraction result: [object Object]", source:  (192)
2025-08-08 00:00:46.153  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-08 00:00:46.153  4029-4029  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":true,\"userName\":\"哈尔滨金包银老刘\",\"lastPostTime\":\"\",\"lastPostTitle\":\"\",\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"timestamp\":1754582446153,\"debug\":{\"url\":\"https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59\",\"title\":\"@哈尔滨金包银老刘 的个人主页\",\"bodyClasses\":\"\",\"allElements\":216,\"hasUserInfo\":true,\"hasTimeInfo\":false,\"hasTitleInfo\":false,\"userNameSelector\":\"DIV.user-name\",\"timeSelector\":\"none\",\"titleSelector\":\"none\",\"timeElementText\":\"none\",\"timeElementAttributes\":[]}}"
2025-08-08 00:00:46.706  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-08 00:00:46.706  4029-4029  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
