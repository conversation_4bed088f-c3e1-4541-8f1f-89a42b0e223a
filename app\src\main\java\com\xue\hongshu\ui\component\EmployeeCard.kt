package com.xue.hongshu.ui.component

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.xue.hongshu.data.entity.EmployeeStatus
import com.xue.hongshu.data.entity.EmployeeWithStats
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun EmployeeCard(
    employeeWithStats: EmployeeWithStats,
    isChecking: Boolean,
    onCheckClick: () -> Unit,
    onDeleteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val employee = employeeWithStats.employee
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 员工基本信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = employee.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "@${employee.xhsUserName}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // 状态指示器
                StatusChip(
                    status = employee.status,
                    isOverdue = employeeWithStats.isOverdue
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 最新发布信息
            if (employee.lastPostTime > 0) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "最新发布:",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        if (employee.lastPostTitle.isNotEmpty()) {
                            Text(
                                text = employee.lastPostTitle,
                                style = MaterialTheme.typography.bodyMedium,
                                maxLines = 1
                            )
                        }
                        Text(
                            text = formatTime(employee.lastPostTime),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    if (employeeWithStats.daysSinceLastPost != Int.MAX_VALUE) {
                        Text(
                            text = "${employeeWithStats.daysSinceLastPost}天前",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (employeeWithStats.isOverdue) {
                                MaterialTheme.colorScheme.error
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )
                    }
                }
            } else {
                Text(
                    text = "暂无发布记录",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 错误信息
            if (employee.status == EmployeeStatus.ERROR && !employee.errorMessage.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "错误: ${employee.errorMessage}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                IconButton(onClick = { showDeleteDialog = true }) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
                
                Button(
                    onClick = onCheckClick,
                    enabled = !isChecking,
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    if (isChecking) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("检查中...")
                    } else {
                        Icon(
                            Icons.Default.Refresh,
                            contentDescription = "检查",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("检查")
                    }
                }
            }
        }
    }
    
    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("确认删除") },
            text = { Text("确定要删除员工 ${employee.name} 吗？") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDeleteClick()
                        showDeleteDialog = false
                    }
                ) {
                    Text("删除")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun StatusChip(
    status: EmployeeStatus,
    isOverdue: Boolean
) {
    val (text, color) = when {
        status == EmployeeStatus.CHECKING -> "检查中" to MaterialTheme.colorScheme.primary
        status == EmployeeStatus.ERROR -> "异常" to MaterialTheme.colorScheme.error
        isOverdue -> "超期" to MaterialTheme.colorScheme.secondary
        else -> "正常" to MaterialTheme.colorScheme.tertiary
    }
    
    AssistChip(
        onClick = { },
        label = { Text(text) },
        colors = AssistChipDefaults.assistChipColors(
            labelColor = color
        )
    )
}

private fun formatTime(timestamp: Long): String {
    return if (timestamp > 0) {
        SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(Date(timestamp))
    } else {
        "未知"
    }
}
