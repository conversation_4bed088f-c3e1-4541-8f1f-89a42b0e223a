package com.xue.hongshu.utils

import android.content.Context
import android.util.Log
import android.webkit.WebView
import com.xue.hongshu.webview.AntiDetectionHelper
import com.xue.hongshu.webview.XhsWebViewClient
import com.xue.hongshu.webview.XhsUserData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object WebViewTester {

    private const val TAG = "WebViewTester"
    
    /**
     * 测试WebView基本功能
     */
    fun testBasicWebView(context: Context, callback: (<PERSON><PERSON><PERSON>, String) -> Unit) {
        try {
            val webView = WebView(context)
            
            // 配置WebView
            AntiDetectionHelper.configureWebView(webView)
            
            // 设置测试客户端
            webView.webViewClient = object : android.webkit.WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    Log.d(TAG, "Test page loaded: $url")
                    
                    // 测试JavaScript执行
                    view?.evaluateJavascript("document.title") { result ->
                        Log.d(TAG, "JavaScript test result: $result")
                        callback(true, "WebView基本功能正常，页面标题: $result")
                    }
                }
                
                override fun onReceivedError(view: WebView?, request: android.webkit.WebResourceRequest?, error: android.webkit.WebResourceError?) {
                    super.onReceivedError(view, request, error)
                    Log.e(TAG, "WebView error: ${error?.description}")
                    callback(false, "WebView加载失败: ${error?.description}")
                }
            }
            
            // 加载测试页面
            webView.loadUrl("https://www.baidu.com")
            
        } catch (e: Exception) {
            Log.e(TAG, "WebView test failed", e)
            callback(false, "WebView测试异常: ${e.message}")
        }
    }
    
    /**
     * 测试小红书页面访问
     */
    fun testXhsAccess(context: Context, xhsUserId: String, callback: (Boolean, String) -> Unit) {
        try {
            val webView = WebView(context)
            
            // 配置WebView
            AntiDetectionHelper.configureWebView(webView)
            
            // 设置测试客户端
            webView.webViewClient = XhsWebViewClient(
                onDataExtracted = { data ->
                    Log.d(TAG, "Data extracted successfully: $data")
                    callback(true, "数据提取成功: 用户名=${data.userName}, 时间=${data.lastPostTime}")
                },
                onError = { error ->
                    Log.e(TAG, "Data extraction failed: $error")
                    callback(false, "数据提取失败: $error")
                },
                onRetry = { errorInfo ->
                    Log.d(TAG, "Retrying data extraction: $errorInfo")
                }
            )
            
            // 加载小红书用户页面
            val url = "https://www.xiaohongshu.com/user/profile/$xhsUserId"
            Log.d(TAG, "Loading XHS page: $url")
            webView.loadUrl(url)
            
        } catch (e: Exception) {
            Log.e(TAG, "XHS access test failed", e)
            callback(false, "小红书访问测试异常: ${e.message}")
        }
    }
    
    /**
     * 测试JavaScript执行
     */
    fun testJavaScriptExecution(context: Context, callback: (Boolean, String) -> Unit) {
        try {
            val webView = WebView(context)
            
            // 配置WebView
            AntiDetectionHelper.configureWebView(webView)
            
            // 设置测试客户端
            webView.webViewClient = object : android.webkit.WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    
                    // 测试复杂JavaScript执行
                    val testScript = """
                        (function() {
                            try {
                                return JSON.stringify({
                                    success: true,
                                    url: window.location.href,
                                    title: document.title,
                                    userAgent: navigator.userAgent,
                                    timestamp: Date.now()
                                });
                            } catch (error) {
                                return JSON.stringify({
                                    success: false,
                                    error: error.message
                                });
                            }
                        })();
                    """.trimIndent()
                    
                    view?.evaluateJavascript(testScript) { result ->
                        Log.d(TAG, "JavaScript execution result: $result")
                        if (result != null && result != "null") {
                            callback(true, "JavaScript执行成功: $result")
                        } else {
                            callback(false, "JavaScript执行失败: 返回null")
                        }
                    }
                }
                
                override fun onReceivedError(view: WebView?, request: android.webkit.WebResourceRequest?, error: android.webkit.WebResourceError?) {
                    super.onReceivedError(view, request, error)
                    callback(false, "页面加载失败: ${error?.description}")
                }
            }
            
            // 加载简单的HTML页面进行测试
            val htmlContent = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>WebView Test Page</title>
                </head>
                <body>
                    <h1>Test Page</h1>
                    <p>This is a test page for WebView functionality.</p>
                </body>
                </html>
            """.trimIndent()
            
            webView.loadDataWithBaseURL("https://test.com", htmlContent, "text/html", "UTF-8", null)
            
        } catch (e: Exception) {
            Log.e(TAG, "JavaScript test failed", e)
            callback(false, "JavaScript测试异常: ${e.message}")
        }
    }
    
    /**
     * 获取WebView调试信息
     */
    fun getWebViewDebugInfo(context: Context): String {
        return try {
            val webView = WebView(context)
            """
            WebView调试信息:
            - WebView版本: ${webView.settings.userAgentString}
            - JavaScript支持: ${webView.settings.javaScriptEnabled}
            - DOM存储支持: ${webView.settings.domStorageEnabled}
            - 数据库支持: ${webView.settings.databaseEnabled}
            - 缓存模式: ${webView.settings.cacheMode}
            - 混合内容模式: ${webView.settings.mixedContentMode}
            """.trimIndent()
        } catch (e: Exception) {
            "获取WebView信息失败: ${e.message}"
        }
    }

    /**
     * 测试SSL连接
     */
    fun testSslConnection(context: Context, url: String, callback: (Boolean, String) -> Unit) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                Log.d(TAG, "开始SSL连接测试: $url")

                val result = SslDiagnosticTool.diagnoseSslConnection(url)

                if (result.isSuccess) {
                    val certInfo = result.certificateInfo
                    val connInfo = result.connectionInfo

                    val message = buildString {
                        appendLine("SSL连接测试成功")
                        certInfo?.let {
                            appendLine("证书主体: ${it.subject}")
                            appendLine("证书颁发者: ${it.issuer}")
                            appendLine("有效期至: ${it.validTo}")
                            appendLine("剩余天数: ${it.daysUntilExpiry}天")
                        }
                        connInfo?.let {
                            appendLine("协议版本: ${it.protocol}")
                            appendLine("加密套件: ${it.cipherSuite}")
                        }
                        if (result.recommendations.isNotEmpty()) {
                            appendLine("建议:")
                            result.recommendations.forEach { recommendation ->
                                appendLine("• $recommendation")
                            }
                        }
                    }

                    callback(true, message)
                } else {
                    val errorMessage = buildString {
                        appendLine("SSL连接测试失败")
                        appendLine("错误: ${result.errorMessage}")
                        if (result.recommendations.isNotEmpty()) {
                            appendLine("建议:")
                            result.recommendations.forEach { recommendation ->
                                appendLine("• $recommendation")
                            }
                        }
                    }
                    callback(false, errorMessage)
                }

            } catch (e: Exception) {
                Log.e(TAG, "SSL连接测试异常", e)
                callback(false, "SSL连接测试异常: ${e.message}")
            }
        }
    }

    /**
     * 批量测试小红书相关域名的SSL连接
     */
    fun testXhsSslConnections(context: Context, callback: (Map<String, Boolean>) -> Unit) {
        val xhsUrls = listOf(
            "https://www.xiaohongshu.com",
            "https://fe-video-qc.xhscdn.com",
            "https://sns-avatar-qc.xhscdn.com",
            "https://ci.xiaohongshu.com",
            "https://edith.xiaohongshu.com",
            "https://picasso.xiaohongshu.com"
        )

        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始批量SSL连接测试")

                val results = SslDiagnosticTool.diagnoseBatch(xhsUrls)
                val resultMap = results.associate { result ->
                    result.url to result.isSuccess
                }

                Log.d(TAG, "批量SSL连接测试完成: $resultMap")

                CoroutineScope(Dispatchers.Main).launch {
                    callback(resultMap)
                }

            } catch (e: Exception) {
                Log.e(TAG, "批量SSL连接测试异常", e)
                CoroutineScope(Dispatchers.Main).launch {
                    callback(emptyMap())
                }
            }
        }
    }
}
