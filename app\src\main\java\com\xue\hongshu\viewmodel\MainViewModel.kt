package com.xue.hongshu.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.xue.hongshu.data.entity.Employee
import com.xue.hongshu.data.entity.EmployeeStatus
import com.xue.hongshu.data.entity.EmployeeWithStats
import com.xue.hongshu.repository.EmployeeRepository
import com.xue.hongshu.webview.XhsUserData
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*

class MainViewModel(private val repository: EmployeeRepository) : ViewModel() {
    
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    private val _employees = repository.getEmployeesWithStats()
    val employees: StateFlow<List<EmployeeWithStats>> = _employees
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    init {
        loadStatistics()
    }
    
    fun addEmployee(name: String, xhsUserId: String, xhsUserName: String) {
        viewModelScope.launch {
            try {
                val employee = Employee(
                    id = UUID.randomUUID().toString(),
                    name = name,
                    xhsUserId = xhsUserId,
                    xhsUserName = xhsUserName,
                    lastPostTime = 0L,
                    lastPostTitle = "",
                    lastCheckTime = 0L,
                    status = EmployeeStatus.ACTIVE
                )
                repository.addEmployee(employee)
                _uiState.value = _uiState.value.copy(
                    message = "员工添加成功",
                    isLoading = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "添加员工失败: ${e.message}",
                    isLoading = false
                )
            }
        }
    }
    
    fun deleteEmployee(employee: Employee) {
        viewModelScope.launch {
            try {
                repository.deleteEmployee(employee)
                _uiState.value = _uiState.value.copy(message = "员工删除成功")
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = "删除员工失败: ${e.message}")
            }
        }
    }
    
    fun startCheckingEmployee(employeeId: String) {
        viewModelScope.launch {
            try {
                repository.updateEmployeeStatus(employeeId, EmployeeStatus.CHECKING)
                _uiState.value = _uiState.value.copy(
                    currentCheckingEmployeeId = employeeId,
                    isChecking = true
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = "开始检查失败: ${e.message}")
            }
        }
    }
    
    fun onEmployeeDataExtracted(employeeId: String, xhsData: XhsUserData) {
        viewModelScope.launch {
            try {
                repository.updateEmployeeWithXhsData(employeeId, xhsData)
                _uiState.value = _uiState.value.copy(
                    message = "数据更新成功",
                    currentCheckingEmployeeId = null,
                    isChecking = false
                )
                loadStatistics()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "更新数据失败: ${e.message}",
                    currentCheckingEmployeeId = null,
                    isChecking = false
                )
            }
        }
    }
    
    fun onEmployeeCheckError(employeeId: String, errorMessage: String) {
        viewModelScope.launch {
            try {
                repository.updateEmployeeError(employeeId, errorMessage)
                _uiState.value = _uiState.value.copy(
                    error = errorMessage,
                    currentCheckingEmployeeId = null,
                    isChecking = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "更新错误状态失败: ${e.message}",
                    currentCheckingEmployeeId = null,
                    isChecking = false
                )
            }
        }
    }
    
    fun startBatchCheck() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isBatchChecking = true,
                batchCheckProgress = 0f
            )
            
            val employeeList = employees.value.map { it.employee }
            val totalCount = employeeList.size
            
            employeeList.forEachIndexed { index, employee ->
                try {
                    // 这里会触发WebView检查
                    startCheckingEmployee(employee.id)
                    
                    // 等待检查完成（实际实现中需要更复杂的同步机制）
                    kotlinx.coroutines.delay(10000) // 假设每个检查需要10秒
                    
                    val progress = (index + 1).toFloat() / totalCount
                    _uiState.value = _uiState.value.copy(batchCheckProgress = progress)
                    
                } catch (e: Exception) {
                    repository.updateEmployeeError(employee.id, e.message ?: "检查失败")
                }
            }
            
            _uiState.value = _uiState.value.copy(
                isBatchChecking = false,
                batchCheckProgress = 0f,
                message = "批量检查完成"
            )
        }
    }
    
    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                val totalCount = repository.getEmployeeCount()
                val activeCount = repository.getEmployeeCountByStatus(EmployeeStatus.ACTIVE)
                val errorCount = repository.getEmployeeCountByStatus(EmployeeStatus.ERROR)
                
                _uiState.value = _uiState.value.copy(
                    statistics = Statistics(
                        totalEmployees = totalCount,
                        activeEmployees = activeCount,
                        errorEmployees = errorCount
                    )
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = "加载统计数据失败: ${e.message}")
            }
        }
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun setLoading(isLoading: Boolean) {
        _uiState.value = _uiState.value.copy(isLoading = isLoading)
    }
}

data class MainUiState(
    val isLoading: Boolean = false,
    val isChecking: Boolean = false,
    val isBatchChecking: Boolean = false,
    val batchCheckProgress: Float = 0f,
    val currentCheckingEmployeeId: String? = null,
    val message: String? = null,
    val error: String? = null,
    val statistics: Statistics = Statistics()
)

data class Statistics(
    val totalEmployees: Int = 0,
    val activeEmployees: Int = 0,
    val errorEmployees: Int = 0,
    val overdueEmployees: Int = 0
)
