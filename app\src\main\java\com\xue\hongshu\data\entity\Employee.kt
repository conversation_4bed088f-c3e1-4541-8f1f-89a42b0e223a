package com.xue.hongshu.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "employees")
data class Employee(
    @PrimaryKey val id: String,
    val name: String,
    val xhsUserId: String,
    val xhsUserName: String,
    val lastPostTime: Long,
    val lastPostTitle: String,
    val lastCheckTime: Long,
    val status: EmployeeStatus,
    val errorMessage: String? = null,
    val checkCount: Int = 0,
    val createdTime: Long = System.currentTimeMillis()
)

enum class EmployeeStatus {
    ACTIVE,      // 正常状态
    INACTIVE,    // 长时间未发布
    ERROR,       // 检查出错
    CHECKING     // 正在检查中
}

data class EmployeeWithStats(
    val employee: Employee,
    val daysSinceLastPost: Int,
    val isOverdue: Boolean
) {
    companion object {
        const val OVERDUE_DAYS = 7 // 7天未发布视为超期
    }
}
