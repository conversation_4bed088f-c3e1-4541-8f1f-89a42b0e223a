package com.xue.hongshu.utils;

/**
 * SSL配置管理器
 * 管理SSL相关的配置和策略
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0010\t\n\u0002\b\n\n\u0002\u0010 \n\u0002\b\u0004\b\u0007\u0018\u0000 \u001f2\u00020\u0001:\u0003\u001f !B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u0006\u0010\u000b\u001a\u00020\fJ\u000e\u0010\r\u001a\u00020\u000e2\u0006\u0010\t\u001a\u00020\nJ\u0006\u0010\u000f\u001a\u00020\u000eJ\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00130\u0011J\u000e\u0010\u0014\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\fJ\u000e\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0017\u001a\u00020\fJ\u0006\u0010\u0018\u001a\u00020\bJ\u0006\u0010\u0019\u001a\u00020\bJ\b\u0010\u001a\u001a\u00020\bH\u0002J\u000e\u0010\u001b\u001a\u00020\b2\u0006\u0010\u001c\u001a\u00020\u000eJ\u0014\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\f0\u001e2\u0006\u0010\u001c\u001a\u00020\u000eR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/xue/hongshu/utils/SslConfigManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "prefs", "Landroid/content/SharedPreferences;", "applyRecommendedConfig", "", "isDebugMode", "", "exportConfig", "", "getRecommendedConfig", "Lcom/xue/hongshu/utils/SslConfigManager$SslConfig;", "getSslConfig", "getSslErrorStats", "Lkotlin/Pair;", "", "", "importConfig", "configString", "isDomainInBypassList", "domain", "recordSslError", "resetSslErrorStats", "suggestConfigAdjustment", "updateSslConfig", "config", "validateSslConfig", "", "Companion", "SslConfig", "TlsVersion", "app_release"})
public final class SslConfigManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "SslConfigManager";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "ssl_config";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_SSL_VERIFICATION_ENABLED = "ssl_verification_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_CERTIFICATE_PINNING_ENABLED = "certificate_pinning_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_ALLOW_SELF_SIGNED = "allow_self_signed";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_MIN_TLS_VERSION = "min_tls_version";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_SSL_ERROR_COUNT = "ssl_error_count";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_LAST_SSL_ERROR_TIME = "last_ssl_error_time";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_SSL_BYPASS_DOMAINS = "ssl_bypass_domains";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEFAULT_MIN_TLS_VERSION = "TLSv1.2";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEFAULT_SSL_BYPASS_DOMAINS = "xiaohongshu.com,xhscdn.com";
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences prefs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.xue.hongshu.utils.SslConfigManager.Companion Companion = null;
    
    public SslConfigManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 获取当前SSL配置
     */
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.utils.SslConfigManager.SslConfig getSslConfig() {
        return null;
    }
    
    /**
     * 更新SSL配置
     */
    public final void updateSslConfig(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.SslConfigManager.SslConfig config) {
    }
    
    /**
     * 检查域名是否在SSL绕过列表中
     */
    public final boolean isDomainInBypassList(@org.jetbrains.annotations.NotNull()
    java.lang.String domain) {
        return false;
    }
    
    /**
     * 记录SSL错误
     */
    public final void recordSslError() {
    }
    
    /**
     * 获取SSL错误统计
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Integer, java.lang.Long> getSslErrorStats() {
        return null;
    }
    
    /**
     * 重置SSL错误统计
     */
    public final void resetSslErrorStats() {
    }
    
    /**
     * 建议配置调整
     */
    private final void suggestConfigAdjustment() {
    }
    
    /**
     * 获取推荐的SSL配置（基于环境）
     */
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.utils.SslConfigManager.SslConfig getRecommendedConfig(boolean isDebugMode) {
        return null;
    }
    
    /**
     * 应用推荐配置
     */
    public final void applyRecommendedConfig(boolean isDebugMode) {
    }
    
    /**
     * 验证SSL配置的合理性
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> validateSslConfig(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.SslConfigManager.SslConfig config) {
        return null;
    }
    
    /**
     * 导出SSL配置（用于备份或分享）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String exportConfig() {
        return null;
    }
    
    /**
     * 导入SSL配置
     */
    public final boolean importConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String configString) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/xue/hongshu/utils/SslConfigManager$Companion;", "", "()V", "DEFAULT_MIN_TLS_VERSION", "", "DEFAULT_SSL_BYPASS_DOMAINS", "KEY_ALLOW_SELF_SIGNED", "KEY_CERTIFICATE_PINNING_ENABLED", "KEY_LAST_SSL_ERROR_TIME", "KEY_MIN_TLS_VERSION", "KEY_SSL_BYPASS_DOMAINS", "KEY_SSL_ERROR_COUNT", "KEY_SSL_VERIFICATION_ENABLED", "PREFS_NAME", "TAG", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * SSL配置数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B=\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u00c6\u0003JA\u0010\u0019\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\u00032\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\nH\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\r\u00a8\u0006\u001f"}, d2 = {"Lcom/xue/hongshu/utils/SslConfigManager$SslConfig;", "", "sslVerificationEnabled", "", "certificatePinningEnabled", "allowSelfSigned", "minTlsVersion", "Lcom/xue/hongshu/utils/SslConfigManager$TlsVersion;", "bypassDomains", "", "", "(ZZZLcom/xue/hongshu/utils/SslConfigManager$TlsVersion;Ljava/util/Set;)V", "getAllowSelfSigned", "()Z", "getBypassDomains", "()Ljava/util/Set;", "getCertificatePinningEnabled", "getMinTlsVersion", "()Lcom/xue/hongshu/utils/SslConfigManager$TlsVersion;", "getSslVerificationEnabled", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_release"})
    public static final class SslConfig {
        private final boolean sslVerificationEnabled = false;
        private final boolean certificatePinningEnabled = false;
        private final boolean allowSelfSigned = false;
        @org.jetbrains.annotations.NotNull()
        private final com.xue.hongshu.utils.SslConfigManager.TlsVersion minTlsVersion = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.Set<java.lang.String> bypassDomains = null;
        
        public SslConfig(boolean sslVerificationEnabled, boolean certificatePinningEnabled, boolean allowSelfSigned, @org.jetbrains.annotations.NotNull()
        com.xue.hongshu.utils.SslConfigManager.TlsVersion minTlsVersion, @org.jetbrains.annotations.NotNull()
        java.util.Set<java.lang.String> bypassDomains) {
            super();
        }
        
        public final boolean getSslVerificationEnabled() {
            return false;
        }
        
        public final boolean getCertificatePinningEnabled() {
            return false;
        }
        
        public final boolean getAllowSelfSigned() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.SslConfigManager.TlsVersion getMinTlsVersion() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getBypassDomains() {
            return null;
        }
        
        public SslConfig() {
            super();
        }
        
        public final boolean component1() {
            return false;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.SslConfigManager.TlsVersion component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.SslConfigManager.SslConfig copy(boolean sslVerificationEnabled, boolean certificatePinningEnabled, boolean allowSelfSigned, @org.jetbrains.annotations.NotNull()
        com.xue.hongshu.utils.SslConfigManager.TlsVersion minTlsVersion, @org.jetbrains.annotations.NotNull()
        java.util.Set<java.lang.String> bypassDomains) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * TLS版本枚举
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/xue/hongshu/utils/SslConfigManager$TlsVersion;", "", "version", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getVersion", "()Ljava/lang/String;", "TLS_1_0", "TLS_1_1", "TLS_1_2", "TLS_1_3", "app_release"})
    public static enum TlsVersion {
        /*public static final*/ TLS_1_0 /* = new TLS_1_0(null) */,
        /*public static final*/ TLS_1_1 /* = new TLS_1_1(null) */,
        /*public static final*/ TLS_1_2 /* = new TLS_1_2(null) */,
        /*public static final*/ TLS_1_3 /* = new TLS_1_3(null) */;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String version = null;
        
        TlsVersion(java.lang.String version) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getVersion() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.xue.hongshu.utils.SslConfigManager.TlsVersion> getEntries() {
            return null;
        }
    }
}