package com.xue.hongshu.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u001c\u0010\n\u001a\u00020\u00062\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\b0\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0016\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0012\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\f0\u0018J\u0018\u0010\u0019\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u000e\u0010\u001a\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0016\u0010\u001c\u001a\u00020\u000f2\u0006\u0010\u001d\u001a\u00020\u001eH\u0086@\u00a2\u0006\u0002\u0010\u001fJ\u001a\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\f0\u00182\u0006\u0010\u001d\u001a\u00020\u001eJ\u0012\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0\f0\u0018J\u0012\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\f0\u0018J\u0010\u0010$\u001a\u00020\u00112\u0006\u0010%\u001a\u00020\u0015H\u0002J\u0016\u0010&\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u001e\u0010\'\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010(\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010)J\u001e\u0010*\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u001d\u001a\u00020\u001eH\u0086@\u00a2\u0006\u0002\u0010+J\u001e\u0010,\u001a\u00020\u00062\u0006\u0010-\u001a\u00020\u00152\u0006\u0010.\u001a\u00020/H\u0086@\u00a2\u0006\u0002\u00100R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/xue/hongshu/repository/EmployeeRepository;", "", "employeeDao", "Lcom/xue/hongshu/data/dao/EmployeeDao;", "(Lcom/xue/hongshu/data/dao/EmployeeDao;)V", "addEmployee", "", "employee", "Lcom/xue/hongshu/data/entity/Employee;", "(Lcom/xue/hongshu/data/entity/Employee;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addEmployees", "employees", "", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "calculateDaysSinceLastPost", "", "lastPostTime", "", "deleteEmployee", "deleteEmployeeById", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllEmployees", "Lkotlinx/coroutines/flow/Flow;", "getEmployeeById", "getEmployeeCount", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getEmployeeCountByStatus", "status", "Lcom/xue/hongshu/data/entity/EmployeeStatus;", "(Lcom/xue/hongshu/data/entity/EmployeeStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getEmployeesByStatus", "getEmployeesWithStats", "Lcom/xue/hongshu/data/entity/EmployeeWithStats;", "getOverdueEmployees", "parseTimeString", "timeString", "updateEmployee", "updateEmployeeError", "errorMessage", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateEmployeeStatus", "(Ljava/lang/String;Lcom/xue/hongshu/data/entity/EmployeeStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateEmployeeWithXhsData", "employeeId", "xhsData", "Lcom/xue/hongshu/webview/XhsUserData;", "(Ljava/lang/String;Lcom/xue/hongshu/webview/XhsUserData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class EmployeeRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.xue.hongshu.data.dao.EmployeeDao employeeDao = null;
    
    public EmployeeRepository(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.dao.EmployeeDao employeeDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.xue.hongshu.data.entity.Employee>> getAllEmployees() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.xue.hongshu.data.entity.EmployeeWithStats>> getEmployeesWithStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.xue.hongshu.data.entity.Employee>> getEmployeesByStatus(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeStatus status) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.xue.hongshu.data.entity.Employee>> getOverdueEmployees() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getEmployeeById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.xue.hongshu.data.entity.Employee> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addEmployee(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addEmployees(@org.jetbrains.annotations.NotNull()
    java.util.List<com.xue.hongshu.data.entity.Employee> employees, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateEmployee(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteEmployee(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.Employee employee, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteEmployeeById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateEmployeeStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateEmployeeWithXhsData(@org.jetbrains.annotations.NotNull()
    java.lang.String employeeId, @org.jetbrains.annotations.NotNull()
    com.xue.hongshu.webview.XhsUserData xhsData, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateEmployeeError(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getEmployeeCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getEmployeeCountByStatus(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.data.entity.EmployeeStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    private final int calculateDaysSinceLastPost(long lastPostTime) {
        return 0;
    }
    
    private final long parseTimeString(java.lang.String timeString) {
        return 0L;
    }
}